#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试登录重定向问题的脚本
"""

import requests
import json

def test_login_and_redirect():
    """测试登录和重定向流程"""
    base_url = "http://localhost:3000"
    
    # 创建会话以保持cookie
    session = requests.Session()
    
    print("=== 测试登录重定向流程 ===")
    
    # 1. 测试登录API
    print("1. 测试登录API...")
    login_url = f"{base_url}/api/user/login"
    login_data = {
        "username": "admin",
        "password": "password",
        "remember": True
    }
    
    try:
        response = session.post(login_url, json=login_data)
        print(f"登录响应状态码: {response.status_code}")
        print(f"登录响应头: {dict(response.headers)}")
        print(f"登录响应Cookie: {response.cookies}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"登录响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get('success'):
                print("✓ 登录成功")
                
                # 2. 测试访问/web页面
                print("\n2. 测试访问/web页面...")
                web_url = f"{base_url}/web"
                
                web_response = session.get(web_url, allow_redirects=False)
                print(f"/web响应状态码: {web_response.status_code}")
                print(f"/web响应头: {dict(web_response.headers)}")
                
                if web_response.status_code == 200:
                    print("✓ /web页面访问成功，返回HTML内容")
                    return True
                elif web_response.status_code == 302:
                    location = web_response.headers.get('Location')
                    print(f"✗ /web页面重定向到: {location}")
                    return False
                else:
                    print(f"✗ /web页面返回异常状态码: {web_response.status_code}")
                    return False
            else:
                print(f"✗ 登录失败: {result.get('message')}")
                return False
        else:
            print(f"✗ 登录请求失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ 测试过程中出现异常: {e}")
        return False

def main():
    """主函数"""
    print("开始测试登录重定向问题...")
    
    success = test_login_and_redirect()
    
    if success:
        print("\n🎉 测试通过！登录重定向问题已修复。")
    else:
        print("\n❌ 测试失败！登录重定向问题仍然存在。")
        print("\n可能的原因：")
        print("1. 服务器未启动")
        print("2. 用户名密码错误")
        print("3. 会话管理配置问题")
        print("4. 路由配置问题")

if __name__ == "__main__":
    main()
