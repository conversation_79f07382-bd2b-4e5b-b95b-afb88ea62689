{% import 'macro/svg.html' as SVG %}
{% import 'macro/head.html' as HEAD %}
<!doctype html>
<html lang="en">
<head>
  {{ HEAD.meta_link() }}
  <title>登录 - NAStool</title>
  <!-- CSS files -->
  <link href="/static/css/tabler.min.css" rel="stylesheet"/>
  <link href="/static/css/style.css" rel="stylesheet"/>
  <style>
    .login_background {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        overflow: hidden;
        background-image: url(data:image/jpg;base64,{{ image_code|safe }});
        background-size: cover;
        background-position: center;
        background-repeat: repeat;
    }
    .login_window {
        background-color: rgba(255,255,255,0.5);
        -webkit-backdrop-filter: blur(1rem);
        backdrop-filter: blur(1rem)
    }
    .image-desc {
        background-color: rgba(34,34,34,.8);
        position: absolute;
        bottom: 20px;
        left: 20px;
        padding: 5px;
        z-index: 10;
        cursor: pointer;
        margin-bottom: env(safe-area-inset-bottom) !important;
        margin-left: env(safe-area-inset-left) !important;
    }
    .image-link,.image-link:hover {
        text-decoration: none;
        color: #fff;
    }
  </style>
</head>
<body class="d-flex flex-column login_background">
{% if image_code and img_title %}
<div class="image-desc card card-md border-0 rounded-3">
  <a class="image-link" href="{{ img_link }}" target="_blank">
    {{ SVG.link() }}
    {{ img_title }}
  </a>
</div>
{% endif %}
<div class="page page-center">
  <div class="container-tight py-4">
    <form class="card card-md border-0 {% if image_code %}login_window{% endif %}" autocomplete="off" onsubmit="return false;">
      <input type="hidden" name="next" value="{{ GoPage }}"/>
      <div class="card-body">
        <div class="text-center mb-3">
          <h1 style="text-align:center;margin: 1.5rem 0">
            <img src="/static/favicon.ico" style="vertical-align: bottom;">
            <label style="font-size: xx-large;color: white; text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);">nastool</label>
          </h1>
        </div>
        <label class="form-label">用户名</label>
        <div class="input-icon mb-3">
            <span class="input-icon-addon">
              {{ SVG.user() }}
            </span>
          <input type="text" name="username" class="form-control {% if err_msg %}is-invalid{% endif %}"
                 placeholder="admin" value="" autocomplete="off">
        </div>
        <label class="form-label">密码</label>
        <div class="input-icon">
            <span class="input-icon-addon">
              {{ SVG.keyboard() }}
            </span>
          <input type="password" class="form-control {% if err_msg %}is-invalid{% endif %}" name="password"
                 placeholder="password" autocomplete="off">
        </div>
        <div class="invalid-feedback mb-3" id="login_retmsg" style="display:block">{{ err_msg }}</div>
        <div class="mb-3">
          <label class="form-check">
            <input type="checkbox" name="remember" class="form-check-input" checked/>
            <span class="form-check-label">保持登录</span>
          </label>
        </div>
        <div class="form-footer">
          <button type="submit" class="btn btn-primary w-100">登录</button>
        </div>
      </div>
    </form>
  </div>
</div>
<script src="/static/js/jquery-3.3.1.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/axios@1.6.0/dist/axios.min.js"></script>
<script src="/static/js/auth-manager.js"></script>
<script src="/static/js/tabler/tabler.min.js"></script>
<script src="/static/js/tabler/demo.min.js"></script>
<script>
// 登录处理
$(document).ready(function() {
    // 延迟检查认证状态，确保authManager完全加载
    setTimeout(() => {
        checkAuthenticationStatus();
    }, 100);

    function checkAuthenticationStatus() {
        // 检查是否已经有有效的认证状态
        if (window.authManager && window.authManager.isAuthenticated()) {
            console.log('检测到本地认证状态，验证Token有效性...');

            // 调用服务器验证Token
            axios.get('/api/auth/verify', {
                headers: {
                    'Authorization': `Bearer ${window.authManager.getAccessToken()}`
                }
            })
            .then(response => {
                if (response.data && response.data.success) {
                    console.log('Token验证成功，重定向到主页');
                    // Token有效，重定向到主页
                    const nextPage = '{{ GoPage }}' || 'index';
                    if (nextPage && nextPage !== 'index') {
                        window.location.href = `/web#${nextPage}`;
                    } else {
                        window.location.href = '/web#index';
                    }
                    return;
                } else {
                    console.log('Token验证失败，清除认证状态');
                    window.authManager.clearAuth();
                }
            })
            .catch(error => {
                console.log('Token验证出错，清除认证状态:', error);
                window.authManager.clearAuth();
            });
        } else {
            // 没有认证状态或authManager未加载，清除可能存在的旧数据
            if (window.authManager) {
                window.authManager.clearAuth();
            }
            console.log('未检测到本地认证状态，显示登录页面');
        }
    }

        // 处理回车键登录
    $('input[name="password"]').on('keypress', function(e) {
        if (e.which === 13) { // Enter键
            performLogin();
        }
    });

    function performLogin() {
        const username = $('input[name="username"]').val();
        const password = $('input[name="password"]').val();
        const remember = $('input[name="remember"]').is(':checked');

        if (!username || !password) {
            showError('请输入用户名和密码');
            return;
        }

        // 显示加载状态
        const submitBtn = $('button[type="button"]');
        const originalText = submitBtn.text();
        submitBtn.prop('disabled', true).text('登录中...');

        // 调用登录API
        axios.post('/api/user/login', {
            username: username,
            password: password,
            remember: remember
        })
        .then(function(response) {
            const data = response.data;
            if (data.success && data.data) {
                // 存储Token和用户信息
                window.authManager.setTokens(
                    data.data.access_token,
                    data.data.refresh_token,
                    data.data.userinfo
                );

                // 重定向到目标页面
                const nextPage = '{{ GoPage }}' || 'index';

                // 使用延迟跳转，确保认证状态完全设置
                setTimeout(() => {
                    if (nextPage && nextPage !== 'index') {
                        window.location.href = `/web#${nextPage}`;
                    } else {
                        window.location.href = '/web#index';
                    }
                }, 100);
            } else {
                showError(data.message || '登录失败');
            }
        })
        .catch(function(error) {
            console.error('Login error:', error);
            if (error.response && error.response.data) {
                showError(error.response.data.message || '登录失败');
            } else {
                showError('网络错误，请重试');
            }
        })
        .finally(function() {
            // 恢复按钮状态
            submitBtn.prop('disabled', false).text(originalText);
        });
    }

    function showError(message) {
        const errorDiv = $('#login_retmsg');
        errorDiv.text(message).show();
        $('input').addClass('is-invalid');
    }

});
</script>
</body>

</html>