{% import 'macro/svg.html' as SVG %}
{% import 'macro/form.html' as FORM %}
{% import 'macro/oops.html' as OOPS %}

<style>

  .text-desc {
      height: 40px;
      overflow-y: auto;
  }

  .components-div {
    border-radius: 5px;
    border: var(--tblr-card-border-width) solid var(--tblr-card-border-color);
    background-color: var(--tblr-card-bg);
    background-clip: border-box;
    margin: 10px;
  }
  .tag-span {
    position: absolute;
    margin-left: -55px;
    --tblr-badge-border-radius: 0;
  }

  #modal-plugin-apps-content{
    --tblr-modal-width: 1400px;
  }

</style>

<div id="downloaders-div" class="card components-div">
  <div class="card-header">
    <h2 class="page-title">下载器</h2>
    <div class="col-auto ms-auto d-print-none">
      <div class="btn-list">
        <a href="javascript:show_add_downloader_modal()" class="btn btn-primary d-none d-sm-inline-block">
          {{ SVG.plus() }}
          新增下载器
        </a>
        <a href="javascript:show_add_downloader_modal()" class="btn btn-primary d-sm-none btn-icon" title="下载目录">
          {{ SVG.plus() }}
        </a>
        <a href="javascript:goto_download_setting()" class="btn d-none d-sm-inline-block">
          {{ SVG.settings() }}
          下载设置
        </a>
        <a href="javascript:goto_download_setting()" class="btn d-sm-none btn-icon" title="下载设置">
          {{ SVG.settings() }}
        </a>
      </div>
    </div>
  </div>
  <div class="page-body">
    <!-- 组件展示 -->
    <div class="container-xl">
      <div class="d-grid gap-3 grid-normal-card">
        <!-- 下载器 -->
        {% if Downloaders %}
        {% for Id, Attr in Downloaders.items() %}
        <a class="card card-link-pop p-0 overflow-hidden rounded-3" href="javascript:void(0)" onclick="show_edit_downloader_modal('{{ Id }}')">
          <div class="card-cover card-cover-blurred text-center"
               {% if DownloaderConf[Attr.type].color %}style="background-color: {{ DownloaderConf[Attr.type].color }}"{% endif %}>
              <span onclick="event.stopPropagation();set_default_downloader('{{ Attr.id }}')"
                    class="btn-action p-1"
                    title="设置默认" style="position:absolute;top:5px;right:5px;z-index:1;"
                    data-bs-toggle="tooltip"
              >
                {% if DefaultDownloader == Id %}
                  {{ SVG.star('icon-filled w-100 h-100 text-purple') }}
                {% else %}
                  {{ SVG.star('w-100 h-100 text-muted') }}
                {% endif %}
              </span>
            <span class="avatar avatar-xl avatar-thumb avatar-rounded" style="background-image: url('{{ DownloaderConf[Attr.type].img_url }}')"></span>
          </div>
          <div class="card-body text-center">
            <div class="card-title mb-1">{% if Attr.enabled == 1 %}<span class="badge bg-green" title="已启用" data-bs-toggle="tooltip"></span>{% endif %} {{ Attr.name }}</div>
            <div class="text-muted">
              {% if Attr.config.get("host")  %}
              {{ Attr.config.get("host") }}:{{ Attr.config.get("port") }}
              {% endif %}
            </div>
            <div class="text-muted mt-2">
              <small>
                {% if Attr.transfer == 1 %}
                <span class="badge bg-green me-1 mb-1" title="转移方式">
                      {{ Attr.rmt_mode_name }}
                    </span>
                {% endif %}
                {% if Attr.only_nastool == 1 %}
                <span class="badge badge-outline text-orange me-1 mb-1" title="标签隔离">
                      标签隔离
                    </span>
                {% endif %}
                {% if Attr.match_path == 1 %}
                <span class="badge badge-outline text-yellow me-1 mb-1" title="目录隔离">
                      目录隔离
                    </span>
                {% endif %}
              </small>
            </div>
          </div>
        </a>
        {% endfor %}
        {% endif %}
      </div>
    </div>
  </div>
</div>

<div id="plugin-div" class="card components-div">
  <div class="card-header">
    <h2 class="page-title">插件</h2>
    <div class="col-auto ms-auto d-print-none">
      <div class="btn-list">
        <a href="javascript:show_plugin_apps_modal()" class="btn btn-primary d-none d-sm-inline-block">
          {{ SVG.apps() }}
          插件市场
        </a>
        <a href="javascript:show_plugin_apps_modal()" class="btn btn-primary d-sm-none btn-icon">
          {{ SVG.apps() }}
        </a>
      </div>
    </div>
  </div>
  {% if PluginCount > 0 %}
  <div class="page-body">
    <!-- 组件展示 -->
    <div class="container-xl">
      <div class="d-grid gap-3 grid-normal-card">
      <!-- 插件 -->
      {% for Id, Plugin in Plugins.items() %}
      <a class="card card-link-pop p-0 rounded-3 overflow-hidden" href="#" data-bs-toggle="modal" data-bs-target="#modal-plugin-{{ Id }}">
        <div class="card-cover card-cover-blurred text-center {% if Plugin.color.startswith('bg-') %}{{ Plugin.color }}{% endif %}"
             {% if Plugin.color.startswith('#') %}style="background-color: {{ Plugin.color }}"{% endif %}>
          <span class="avatar avatar-xl avatar-thumb avatar-rounded" {% if Plugin.icon and not Plugin.icon.startswith('http') %}
            style="background-image: url('../static/img/plugins/{{ Plugin.icon }}')"
            {% elif Plugin.icon %}
            style="background-image: url('{{ Plugin.icon }}')"
            {% endif %}
            >
            {% if not Plugin.icon %}
            {{ Plugin.name|first }}
            {% endif %}
          </span>
        </div>
        <div class="card-body text-center">
          <div class="card-title mb-1">{% if Plugin.state %}<span class="badge bg-green"></span>{% endif %} {{ Plugin.name }}</div>
          <div class="text-muted">{{ Plugin.desc }}</div>
        </div>
      </a>
      {% endfor %}
      </div>
    </div>
  </div>
  {% else %}
  {{ OOPS.empty('没有插件', '没有安装任何插件，请前往插件市场选择安装。') }}
  {% endif %}
</div>

<div class="page-body">
  <!-- 弹窗 -->
  <!-- 下载器部分 -->
  <div class="modal modal-blur fade" id="modal-downloader" tabindex="-1" role="dialog" aria-hidden="true"
       data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="downloader_modal_title">新增下载器</h5>
          <input type="hidden" id="downloader_id">
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="row" id="div_downloader_normal">
            <div class="col-lg-9">
              <div class="mb-3">
                <label class="form-label required">名称</label>
                <input type="text" id="downloader_name" class="form-control" placeholder="别名">
              </div>
            </div>
            <div class="col-lg-3">
              <div class="mb-3">
                <label class="form-label required">状态</label>
                <select class="form-select" id="downloader_enabled">
                  <option value="1" selected>启用</option>
                  <option value="0">停用</option>
                </select>
              </div>
            </div>
          </div>
          <div class="form-selectgroup-boxes row mb-3">
            <label class="form-label required">类型 <span class="form-help" data-bs-toggle="tooltip"
                                                          data-bs-original-title="QB版本要求>=4.3.9，TR版本要求>=3.0，其它版本可能存在适配问题">?</span></label>
            {% for Id, Attr in DownloaderConf.items() %}
              <div class="col-lg">
                <div class="mb-1">
                  <label class="form-selectgroup-item" for="type_{{ Id }}">
                    <input type="radio" name="downloader_type" id="type_{{ Id }}" value="{{ Id }}"
                           class="form-selectgroup-input" checked>
                    <span class="form-selectgroup-label d-flex align-items-center p-2">
                    <span class="me-2">
                      <span class="form-selectgroup-check"></span>
                    </span>
                    <span class="form-selectgroup-label-content d-flex align-items-center">
                      <span class="avatar avatar-sm avatar-thumb avatar-rounded"
                            style="background-image: url({{ Attr.img_url }})"></span>
                      <span class="form-selectgroup-title strong ms-1">{{ Attr.name }}</span>
                    </span>
                  </span>
                  </label>
                </div>
              </div>
            {% endfor %}
          </div>
          {% for Id, Attr in DownloaderConf.items() %}
          <div id="div_downloader_config_{{ Id }}">
            {{ FORM.gen_form_empty_elements(Attr.config) }}
          </div>
          {% endfor %}
          <div class="row" id="div_downloader_transfer">
            <div class="col-lg">
              <div class="mb-3">
                <label class="form-label required">监控 <span class="form-help" data-bs-toggle="tooltip"
                                                          data-bs-original-title="监控下载软件，下载完成后自动进行文件转移，与目录同步监控下载目录二选一开启">?</span></label>
                <select class="form-select" id="downloader_transfer">
                  <option value="1">是</option>
                  <option value="0" selected>否</option>
                </select>
              </div>
            </div>
            <div class="col-lg">
              <div class="mb-3">
                <label class="form-label required">转移方式</label>
                <select class="form-select" id="downloader_rmt_mode">
                  {% for mode in RmtModeDict %}
                    <option value="{{ mode.value }}">{{ mode.name }}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
            <div class="col-lg">
              <div class="mb-3">
                <label class="form-label required">标签隔离 <span class="form-help" data-bs-toggle="tooltip"
                                                          data-bs-original-title="启用后只有含NASTOOL标签的下载任务才会被自动转移和显示，关闭则下载软件中所有的任务都会转移和显示">?</span></label>
                <select class="form-select" id="downloader_only_nastool">
                  <option value="1">是</option>
                  <option value="0" selected>否</option>
                </select>
              </div>
            </div>
            <div class="col-lg">
              <div class="mb-3">
                <label class="form-label required">目录隔离 <span class="form-help" data-bs-toggle="tooltip" data-bs-original-title="启用后只有在下载目录中的下载任务才会被自动转移和显示">?</span></label>
                <select class="form-select" id="downloader_match_path">
                  <option value="1">是</option>
                  <option value="0" selected>否</option>
                </select>
              </div>
            </div>
          </div>
          <details class="mb-3">
            <summary class="summary required">下载目录设置
            <span class="form-help" title="根据类型及二级分类自动选择下载目录，按优先级从前往后依次匹配，直到找到符合条件及空间要求的目录下载；二级分类从基础设置->二级分类策略的配置中读取，如未配置二级分类则无法配置下载目录二级分类"
                  data-bs-toggle="tooltip">?</span>
            </summary>
            <div class="row mt-2" id="div_downloader_downloaddir"></div>
            <div class="ms-2 mt-2">
              <a href="javascript:add_downloaddir()" class="btn-icon" title="增加目录">{{ SVG.folder_plus() }}</a>
            </div>
          </details>
        </div>
        <div class="modal-footer">
          <button onclick="add_or_edit_or_test_downloader('test')" id="test_downloader_btn" class="btn me-auto">
            测试
          </button>
          <button onclick="delete_downloader()" id="delete_downloader_client_btn"
             class="btn btn-link text-danger">
            删除
          </button>
          <button onclick="add_or_edit_or_test_downloader('save')" id="add_or_edit_downloader_btn" class="btn btn-primary">
            确定
          </button>
        </div>
      </div>
    </div>
  </div>
  <!-- 插件部分 -->
  {% for Id, Plugin in Plugins.items() %}
  <plugin-modal
          plugin-id="{{ Id }}"
          plugin-name="{{ Plugin.name }}"
          plugin-config='{{ Plugin.config|tojson|safe }}'
          plugin-fields='{{ Plugin.fields|tojson|safe }}'
          plugin-prefix="{{ Plugin.prefix }}"
          plugin-page="{{ Plugin.page }}"
  >
  </plugin-modal>
  {% endfor %}
  <!-- 插件市场容器 -->
  <div class="modal modal-blur fade" id="modal-plugin-apps" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable" id="modal-plugin-apps-content" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">插件市场</h5>
          <button type="button" class="btn-action" aria-label="Settings" onclick="show_external_settings_page()">{{ SVG.settings() }}</button>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div>
          <ul class="nav nav-fill card-header-tabs nav-tabs rounded-3" data-bs-toggle="tabs" role="tablist">
            <li class="nav-item" role="presentation">
              <a href="javascript:void(0)" onclick="load_plugin_apps_modal()" class="nav-link active" style="justify-content: center" data-bs-toggle="tab" aria-selected="true" role="tab">官方插件</a>
            </li>
            <li class="nav-item" role="presentation">
              <a href="javascript:void(0)" onclick="load_external_plugin_apps_modal()" class="nav-link" style="justify-content: center" data-bs-toggle="tab" aria-selected="false" role="tab" tabindex="-1">第三方插件</a>
            </li>
          </ul>
        </div>
        <div class="modal-body" style="min-height:800px;">
          <div class="d-grid gap-3 grid-normal-card align-items-start" id="plugin_apps_content" style="display:none;"></div>
        </div>
      </div>
    </div>
  </div>
  <!-- 第三方插件设置页 -->
  <div class="modal modal-blur fade" id="modal-external-source-settings" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">第三方源管理</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div id="plugin_external_page_content">
          <div class="modal-body" style="overflow-y: auto">
              <div class="row mb-2">
                  <div class="col-12 col-lg" style="display:block">
                    <div class="mb-1">
                      <label class="form-label false">
                      第三方源
                      <span class="form-help" data-bs-toggle="tooltip" data-bs-original-title="配置正确的第三方源以后将会显示再第三方插件仓库中，安装以后会进入插件市场。">?</span></label>
                      <textarea class="form-control" id="external_source_content" rows="10" placeholder="每行一个源地址，可以同时添加多个源 ... &#10;https://ip:prot/route/xxx.json &#10;https://ip:prot/route/xxx.json">{{ Settings }}</textarea>
                    </div>
                  </div>
              </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-primary" id="modal_external_settings_btn">保存</button>
        </div>
      </div>
    </div>
  </div>
  <!-- 插件详情页 -->
  <div class="modal modal-blur fade" id="modal-plugin-page" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="plugin_page_title"></h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div id="plugin_page_content">
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-primary" id="modal_plugin_page_btn">确定</button>
        </div>
      </div>
    </div>
  </div>
</div>

<script id="div_downloader_downloaddir_template" type="text/html">
  <div class="row mt-2" id="dirdiv_{DIRDIV_LEVEL}">
    <div class="col-lg-12">
      <div class="row">
        <div class="col-12 col-lg mb-1">
          <select class="form-select" name="dir_type" id="dir_type_{DIRDIV_LEVEL}" onchange="get_categories($(this).attr('id').replace('dir_type_', ''))">
            <option value="" selected>全部</option>
            <option value="电影">电影</option>
            <option value="电视剧">电视剧</option>
            <option value="动漫">动漫</option>
          </select>
        </div>
        <div class="col-12 col-lg mb-1">
          <select class="form-select" name="dir_subtype" id="dir_subtype_{DIRDIV_LEVEL}">
            <option value="" selected>全部</option>
          </select>
        </div>
        <div class="col-12 col-lg mb-1">
          <input type="text" value="" id="path_label_{DIRDIV_LEVEL}" class="form-control" placeholder="分类标签，仅QB有效" autocomplete="off">
        </div>
      </div>
      <div class="row">
        <div class="col-12 col-lg mb-1">
          <input type="text" value="" id="save_path_{DIRDIV_LEVEL}" class="form-control" placeholder="下载保存目录" autocomplete="off">
        </div>
        <div class="col-12 col-lg mb-1">
          <input type="text" value="" id="container_path_{DIRDIV_LEVEL}" class="form-control" placeholder="NAStool访问目录" autocomplete="off">
        </div>
        <div class="col-12 col-lg mb-1 align-self-center" style="text-align: center">
          <a href="javascript:del_downloaddir('{DIRDIV_LEVEL}')" class="btn-icon text-red" title="删除目录">
            {{ SVG.circle_minus() }}
          </a>
        </div>
      </div>
    </div>
  </div>
</script>

<script type="text/javascript">

  // 恢复输入项为默认值
  function reset_downloader_config_fields() {
    {% for Id, Attr in DownloaderConf.items() %}
      {% for FieldId, FieldAttr in Attr.config.items() %}
        {% if FieldAttr.type == "switch" %}
          {% if FieldAttr.default %}
            $("#{{ FieldAttr.id }}").prop('checked', true);
          {% else %}
            $("#{{ FieldAttr.id }}").prop('checked', false);
          {% endif %}
        {% else %}
          {% if FieldAttr.default %}
            $("#{{ FieldAttr.id }}").val('{{ FieldAttr.default }}');
          {% else %}
            $("#{{ FieldAttr.id }}").val('');
          {% endif %}
        {% endif %}
      {% endfor %}
    {% endfor %}
  }

  // 显示新增下载器
  function show_add_downloader_modal() {
    $("#downloader_id").val('');
    $("#downloader_modal_title").text("新增下载器");
    reset_downloader_config_fields();
    show_downloader_type("{{ DownloaderConf.keys()|first }}");
    $("#downloader_transfer").val("0");
    $("#downloader_only_nastool").val("0");
    $("#downloader_match_path").val("0");
    $("#downloader_rmt_mode").val("link");
    CURRENT_DIRDIV_LEVEL = 0;
    $("#div_downloader_downloaddir").empty();
    add_downloaddir();
    $("#test_downloader_btn").text("测试").attr("disabled", false);
    $("#delete_downloader_client_btn").hide();
    $("#modal-downloader").modal("show");
  }

  // 显示编辑下载器
  function show_edit_downloader_modal(did){
    $("#downloader_id").val(did);
    $("#downloader_modal_title").text("编辑下载器");
    axios_post("get_downloaders", {did: did}, function (ret) {
      if (ret.code === 0) {
        let detail = ret.detail;
        $("#downloader_name").val(detail.name);
        $("#downloader_enabled").val(detail.enabled);
        // 清空输入项
        reset_downloader_config_fields();
        let type = detail.type;
        switch (type) {
          {% for Id, Attr in DownloaderConf.items() %}
          case "{{ Id }}":
            {% for FieldId, FieldAttr in Attr.config.items() %}
              {% if FieldAttr.type == "switch" %}
                if (detail.config.{{ FieldId }}) {
                  $("#{{ FieldAttr.id }}").prop("checked", true);
                } else {
                  $("#{{ FieldAttr.id }}").prop("checked", false);
                }
              {% else %}
                $("#{{ FieldAttr.id }}").val(detail.config.{{ FieldId }});
              {% endif %}
            {% endfor %}
            break;
          {% endfor %}
          default:
            break;
        }
        show_downloader_type(type);
        $("#downloader_transfer").val(detail.transfer);
        $("#downloader_only_nastool").val(detail.only_nastool);
        $("#downloader_match_path").val(detail.match_path);
        $("#downloader_rmt_mode").val(detail.rmt_mode);
        $("#test_message_client_btn").text("测试").attr("disabled", false);
        let download_dirs = detail.download_dir
        CURRENT_DIRDIV_LEVEL = 0;
        $("#div_downloader_downloaddir").empty();
        for (let download_dir of download_dirs) {
          add_downloaddir(download_dir.type, download_dir.category, download_dir.save_path, download_dir.container_path, download_dir.label);
        }
        $("#delete_downloader_client_btn").show();
        $("#modal-downloader").modal("show");
      }
    });
  }

  // 检查下载器
  function check_downloader(flag, did, checked){
    axios_post("check_downloader", {flag: flag, did: did, checked: checked}, function (ret) {
      if (ret.code === 0) {
        window_history_refresh();
      }
    });
  }

  // 删除下载器
  function delete_downloader(){
    let did = $("#downloader_id").val();
    let name = $("#downloader_name").val();
    $("#modal-downloader").modal('hide');
    show_confirm_modal("删除下载器 " + name + " ？", function () {
      hide_confirm_modal();
      axios_post("del_downloader", { "did": did }, function (ret) {
        window_history_refresh();
      });
    });
  }

  // 新增/编辑下载器
  function add_or_edit_or_test_downloader(action) {
    let type = $('input:radio[name=downloader_type]:checked').val();
    let config = {};
    let transfer = 0;
    let only_nastool = 0;
    let match_path = 0;
    let rmt_mode = "";
    switch (type) {
      {% for Id, Attr in DownloaderConf.items() %}
      case "{{ Id }}":
        {% for FieldId, FieldAttr in Attr.config.items() %}
          {% if FieldAttr.type == "switch" %}
            if ($("#{{ FieldAttr.id }}").prop("checked")) {
              config.{{ FieldId }} = 1;
            } else {
              config.{{ FieldId }} = 0;
            }
          {% else %}
            config.{{ FieldId }} = $("#{{ FieldAttr.id }}").val();
            {% if FieldAttr.required %}
              if (!config.{{ FieldId }}) {
                $("#{{ FieldAttr.id }}").addClass("is-invalid");
                return;
              } else {
                $("#{{ FieldAttr.id }}").removeClass("is-invalid");
              }
            {% endif %}
          {% endif %}
        {% endfor %}
        {% if Attr.monitor_enable %}
          transfer = $("#downloader_transfer").val();
          only_nastool = $("#downloader_only_nastool").val();
          match_path = $("#downloader_match_path").val();
          rmt_mode = $("#downloader_rmt_mode").val();
        {% else %}
          transfer = 0;
          only_nastool = 0;
          match_path = 0;
          rmt_mode = "";
        {% endif %}
        break;
      {% endfor %}
      default:
        break;
    }
    let name_obj = $("#downloader_name");
    let name = name_obj.val();
    if (!name) {
      name_obj.addClass("is-invalid");
      return;
    } else {
      name_obj.removeClass("is-invalid");
    }
    // 下载目录设置
    let download_dir = [];
    $("[id^='dirdiv_']").each(function () {
      let suffix = $(this).attr('id').replace("dirdiv_", "");
      download_dir.push({
        save_path: $(`#save_path_${suffix}`).val(),
        type: $(`#dir_type_${suffix}`).val(),
        category: $(`#dir_subtype_${suffix}`).val(),
        container_path: $(`#container_path_${suffix}`).val(),
        label: $(`#path_label_${suffix}`).val()
      });
    });
    const params = {
      did: $("#downloader_id").val(),
      name: name,
      enabled: $("#downloader_enabled").val(),
      transfer: transfer,
      only_nastool: only_nastool,
      match_path: match_path,
      rmt_mode: rmt_mode,
      type: type,
      config: JSON.stringify(config),
      download_dir: JSON.stringify(download_dir)
    };
    switch (action) {
      case "save":
        let save_btn = $("#add_or_edit_downloader_btn");
        save_btn.text("保存中").attr("disabled", true);
        axios_post("update_downloader", params, function (ret) {
          $("#modal-downloader").modal('hide');
          save_btn.attr("disabled", false);
          window_history_refresh();
        });
        break;
      case "test":
        let test_btn = $("#test_downloader_btn");
        test_btn.text("测试中").attr("disabled", true);
        axios_post("test_downloader", params, function (ret) {
          if (ret.code === 0) {
            test_btn.text("测试成功").attr("disabled", false);
          } else {
            test_btn.text("测试失败！").attr("disabled", false);
          }
        });
        break;
      default:
        break;
    }
  }

  var CURRENT_DIRDIV_LEVEL = 0;
  //添加下载目录
  function add_downloaddir(dir_type, dir_subtype, save_path, container_path, label) {
    let html = $("#div_downloader_downloaddir_template").text();
    $("#div_downloader_downloaddir").append(html.replaceAll('{DIRDIV_LEVEL}', CURRENT_DIRDIV_LEVEL));
    $(`#container_path_${CURRENT_DIRDIV_LEVEL}`).attr("onclick", "openFileBrowser(this,$(this).val(),'',true,false);");
    if (dir_type) {
      $(`#dir_type_${CURRENT_DIRDIV_LEVEL}`).val(dir_type);
      get_categories(CURRENT_DIRDIV_LEVEL.toString())
    }
    if (dir_subtype) {
      $(`#dir_subtype_${CURRENT_DIRDIV_LEVEL}`).val(dir_subtype);
    }
    if (save_path) {
      $(`#save_path_${CURRENT_DIRDIV_LEVEL}`).val(save_path);
    }
    if (container_path) {
      $(`#container_path_${CURRENT_DIRDIV_LEVEL}`).val(container_path);
    }
    if (label) {
      $(`#path_label_${CURRENT_DIRDIV_LEVEL}`).val(label);
    }
    CURRENT_DIRDIV_LEVEL = CURRENT_DIRDIV_LEVEL + 1;
  }

  //删除下载目录
  function del_downloaddir(id) {
    if (id) {
      $(`#dirdiv_${id}`).remove();
    }
  }

  //获取二级分类
  function get_categories(id) {
    if (!id) {
      return;
    }
    let dir_type = $(`#dir_type_${id}`).val();
    let subtype_obj = $(`#dir_subtype_${id}`);
    if (!dir_type || !subtype_obj) {
      return;
    }
    let sub_types = {{ Categories|safe }}[dir_type];
    let subtypes_used = [];
    $("[id^='dirdiv_']").each(function () {
      let suffix = $(this).attr('id').replace("dirdiv_", "");
      let type = $(`#dir_type_${suffix}`).val();
      let subtype = $(`#dir_subtype_${suffix}`).val();
      if (type === dir_type && subtype) {
        subtypes_used.push(subtype);
      }
    });
    let subtype_content = `<option value="" selected>全部</option>`;
    for (let sub_type of sub_types) {
      if (!subtypes_used.includes(sub_type)) {
        subtype_content += `<option value="${sub_type}">${sub_type}</option>`;
      }
    }
    subtype_obj.empty().append(subtype_content);
  }

  // 单选框事件
  $('input[type=radio][name=downloader_type]').change(function () {
    show_downloader_type(this.value);
  });

  // 下载器类型类型
  function show_downloader_type(type) {
    switch (type) {
      {% for Id, Attr in DownloaderConf.items() %}
      case "{{ Id }}":
        $("div[id^='div_downloader_config']").each(function () {
          $(this).hide();
        });
        $("#div_downloader_config_{{ Id }}").show();
        {% if Attr.get("monitor_enable") %}
          $("#div_downloader_transfer").show();
        {% else %}
          $("#div_downloader_transfer").hide();
        {% endif %}
        $("#type_{{ Id }}").prop("checked", true);
        break;
      {% endfor %}
      default:
        break;
    }
  }

  // 设置默认下载器
  function set_default_downloader(did){
    let params = {
      'key': "DefaultDownloader",
      'value': did
    };
    axios_post("set_system_config", params, function (ret) {
      window_history_refresh();
    });
  }

  // 跳转下载设置
  function goto_download_setting(){
    var downloaders = {{ Downloaders | tojson }};
    if (!Object.keys(downloaders).length) {
      show_fail_modal("请先添加下载器！");
    } else {
      navmenu('download_setting');
    }
  }
</script>

<!-- 插件业务函数 -->
<script type="text/javascript">
  // 保存配置
  function save_config_plugin(id, prefix, func) {
    let params = input_select_GetVal(`modal-plugin-${id}`, prefix);
    let selectgroup_config = {};
    $(`#modal-plugin-${id} div[class^='form-selectgroup']`).each(function(){
      let name = $(this).attr("id");
      selectgroup_config[name.replace(prefix, "")] = select_GetSelectedVAL(name);
    })
    params = {...params, ...selectgroup_config}
    console.log(params)
    axios_post("update_plugin_config", {plugin: id, config: params}, func);
  }

  //保存配置、关闭和刷新页面
  function save_plugin_config(plugin, prefix) {
    $("#modal-plugin-" + plugin).modal('hide');
    save_config_plugin(plugin, prefix, function (ret) {
      window_history_refresh();
    });
  }

  // 显示插件市场页面
  function show_plugin_apps_modal() {
    $("#modal-plugin-apps").modal('show');
    load_plugin_apps_modal();
  }

  // 加载官方插件
  function load_plugin_apps_modal() {
    $("#plugin_apps_content").empty();
    // 获取插件列表
    axios_post("get_plugin_apps", {}, function(ret) {
      if (ret.result) {
        for (let pid in ret.result) {
          let plugin = ret.result[pid];
          let count = ret.statistic[pid];
          let plugin_html = `
            <div class="card card-link-pop card-borderless p-0 shadow-sm rounded-3 overflow-hidden" href="javascript:void(0)">
              <div class="card-cover card-cover-blurred text-center ${plugin.color.startsWith('bg-') ? plugin.color:''}"
                ${plugin.color.startsWith('#') ? `style="background-color: ${plugin.color}"` : ''}
              >
                <span class="avatar avatar-xl avatar-thumb avatar-rounded"
                    style="background-image: url('../static/img/plugins/${plugin.icon}')"
                >
                </span>
                ${count ? `<span class="badge bg-dark position-absolute" style="top: 10px; right: 10px;">${count.toLocaleString()}</span>` : ''}
              </div>
              <div class="card-body text-start">
                <div class="card-title mb-1">${plugin.name}</div>
                <div class="text-muted text-desc">${plugin.desc}</div>
                <div class="text-muted mt-1"><strong>作者：</strong>
                ${plugin.author_url ? `<a href="${plugin.author_url}" target="_blank">${plugin.author}</a>`
                  : `<span class="text-muted">${plugin.author}</span>`}
                </div>
              </div>
              <div class="d-flex">
                ${plugin.installed ? `
                  <a href="javascript:uninstall_plugin('${pid}', '${plugin.name}')" class="card-btn text-danger">
                    {{ SVG.trash('me-2') }}
                     卸载
                  </a>
                  ` : `
                  <a href="javascript:install_plugin('${pid}', '${plugin.name}')" class="card-btn">
                    {{ SVG.cloud_download('me-2') }}
                     安装
                  </a>
                  `}
              </div>
            </div>
          `;
          $("#plugin_apps_content").append(plugin_html);
        }
        $("#plugin_apps_content").show();
      }
    });
  }

  // 加载第三方插件
  function load_external_plugin_apps_modal() {
    $("#plugin_apps_content").empty();
    // 获取插件列表
    axios_post("get_external_plugin_apps", {}, function(ret) {
      if (ret.result) {
        for (let pid in ret.result) {
          let plugin = ret.result[pid];
          let count = ret.statistic[pid];

          let plugin_html = `
            <div class="card card-link-pop card-borderless p-0 shadow-sm rounded-3 overflow-hidden" href="javascript:void(0)">
              <div class="card-cover card-cover-blurred text-center ${plugin.color.startsWith('bg-') ? plugin.color:''}"
                ${plugin.color.startsWith('#') ? `style="background-color: ${plugin.color}"` : ''}
              >
                <span class="avatar avatar-xl avatar-thumb avatar-rounded"
                    style="background-image: url('../static/img/plugins/${plugin.icon.split('/').pop()}')"
                >
                </span>
                ${count ? `<span class="badge bg-dark position-absolute" style="top: 10px; right: 10px;">${count.toLocaleString()}</span>` : ''}
              </div>
              <div class="card-body text-start">
                <div class="card-title mb-1">${plugin.name}</div>
                <div class="text-muted"><strong>描述：</strong>${plugin.desc}</div>
                <div class="text-muted mt-1"><strong>作者：</strong>
                ${plugin.author_url ? `<a href="${plugin.author_url}" target="_blank">${plugin.author}</a>`
                  : `<span class="text-muted">${plugin.author}</span>`}
                </div>
              </div>
              <div class="d-flex">
                ${plugin.installed ? `
                  <a href="javascript:uninstall_external_plugin('${pid}', '${plugin.name}')" class="card-btn text-danger">
                    {{ SVG.trash('me-2') }}
                    卸载
                  </a>
                  ` : `
                  <a href="javascript:install_external_plugin('${pid}', '${plugin.name}', '${plugin.download_url}', '${plugin.file_md5}')" class="card-btn">
                    {{ SVG.cloud_download('me-2') }}
                    安装
                  </a>
                  `}
                ${ plugin.update ? `
                  <a href="javascript:update_external_plugin('${pid}', '${plugin.name}', '${plugin.download_url}', '${plugin.file_md5}')" class="card-btn">
                    {{ SVG.cloud_download('me-2') }}
                    更新
                  </a>
                  ` : ``}
              </div>
            </div>
          `;
          $("#plugin_apps_content").append(plugin_html);
        }
        $("#plugin_apps_content").show();
      }
    });
  }

  // 显示第三方插件设置页面
  function show_external_settings_page() {
      $("#modal-external-source-settings").modal('show');
      $("#modal_external_settings_btn").click(function (){
          var value = $("#external_source_content").val();
          axios_post("save_external_source_settings", {value: value}, function(ret){
            if (ret.code === 0) {
                show_success_modal(ret.msg, function (){
                  $("#modal-external-source-settings").modal('hide');
                });
            } else {
                show_fail_modal(ret.msg)
            }
          })
      })
  }

  // 显示插件页面
  function show_plugin_extra_page(id) {
    axios_post("get_plugin_page", {id: id}, function(ret){
      if (ret.code === 0) {
        $("#modal-plugin-" + id).modal('hide');
        $("#plugin_page_title").text(ret.title);
        $("#plugin_page_content").html(ret.content);
        if (ret.func) {
          $("#modal_plugin_page_btn").unbind('click').click(function(){
            eval(ret.func);
          });
        } else {
          $("#modal_plugin_page_btn").unbind('click').click(function(){
            $('#modal-plugin-page').modal('hide');
          });
        }
        $("#modal-plugin-page").modal('show');
      }
    });
  }

  // 安装插件
  function install_plugin(id, name) {
    axios_post("install_plugin", {id: id}, function(ret){
      $("#modal-plugin-apps").modal('hide');
      if (ret.code === 0) {
        show_success_modal(name + " 插件安装成功！", function (){
          window_history_refresh();
        });
      } else {
        show_fail_modal(ret.msg, function (){
          $("#modal-plugin-apps").modal('show');
        });
      }
    });
  }

  // 卸载插件
  function uninstall_plugin(id, name) {
    axios_post("uninstall_plugin", {id: id}, function(ret){
      $("#modal-plugin-apps").modal('hide');
      if (ret.code === 0) {
        show_warning_modal(name + " 插件已卸载！", function (){
          window_history_refresh();
        });
      } else {
        show_fail_modal(ret.msg, function (){
          $("#modal-plugin-apps").modal('show');
        });
      }
    });
  }

  // 安装第三方插件
  function install_external_plugin(id, name, download_url, file_md5) {
    var params = {
        id: id,
        file_md5: file_md5,
        download_url: download_url
    }
    axios_post("install_external_plugin", params, function(ret) {
      $("#modal-external-plugin-apps").modal('hide');
      if (ret.code === 0) {
        show_success_modal(name + " 插件安装成功！", function (){
          window_history_refresh();
        });
      } else {
        show_fail_modal(ret.msg, function (){
          $("#modal-external-plugin-apps").modal('show');
        });
      }
    });
  }

  // 卸载第三方插件
  function uninstall_external_plugin(id, name) {
      var params = {
          id: id,
      }
      axios_post("uninstall_external_plugin", params, function(ret){
      $("#modal-external-plugin-apps").modal('hide');
      if (ret.code === 0) {
        show_warning_modal(name + " 插件已卸载！", function (){
          window_history_refresh();
        });
      } else {
        show_fail_modal(ret.msg, function (){
          $("#modal-external-plugin-apps").modal('show');
        });
      }
    });
  }

  // 更新第三方插件
  function update_external_plugin(id, name, download_url, file_md5) {
    var params = {
        id: id,
        file_md5: file_md5,
        download_url: download_url
    }
    axios_post("install_external_plugin", params, function(ret){
        $("#modal-external-plugin-apps").modal('hide');
        if (ret.code === 0) {
          show_success_modal(name + " 插件更新成功！更新的插件需要重启生效!", function (){
            window_history_refresh();
          });
        } else {
          show_fail_modal(ret.msg, function (){
            $("#modal-external-plugin-apps").modal('show');
          });
        }
    })
}
</script>

<script type="text/javascript">
  $(document).ready(function(){
    fresh_tooltip();
  });
</script>
<script type="text/javascript">
  // 插件自带脚本函数
  {% for Id, Plugin in Plugins.items() %}
    {% if Plugin.script %}
      {{ Plugin.script|safe }}
    {% endif %}
  {% endfor %}
</script>