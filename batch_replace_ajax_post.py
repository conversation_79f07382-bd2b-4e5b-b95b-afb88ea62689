#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
批量替换ajax_post为axios_post的脚本
"""

import os
import re
import sys
from pathlib import Path

def replace_ajax_post_in_file(file_path):
    """在单个文件中替换ajax_post为axios_post"""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # 记录原始内容
        original_content = content
        
        # 替换ajax_post调用（但不替换函数定义）
        # 使用正则表达式匹配ajax_post(，但排除function ajax_post(
        pattern = r'(?<!function\s)ajax_post\s*\('
        replacement = 'axios_post('
        
        content = re.sub(pattern, replacement, content)
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        return False
        
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        return False

def batch_replace_ajax_post(directory):
    """批量替换目录中的ajax_post调用"""
    
    # 需要处理的文件扩展名
    extensions = ['.js', '.html', '.htm']
    
    # 排除的目录
    exclude_dirs = {
        '__pycache__', 
        '.git', 
        'node_modules', 
        '.vscode',
        'venv',
        'env'
    }
    
    # 排除的文件（保留原始ajax_post定义）
    exclude_files = {
        'util.js'  # 保留原始ajax_post函数定义
    }
    
    processed_files = []
    modified_files = []
    
    for root, dirs, files in os.walk(directory):
        # 排除特定目录
        dirs[:] = [d for d in dirs if d not in exclude_dirs]
        
        for file in files:
            if any(file.endswith(ext) for ext in extensions):
                if file in exclude_files:
                    continue
                    
                file_path = os.path.join(root, file)
                processed_files.append(file_path)
                
                if replace_ajax_post_in_file(file_path):
                    modified_files.append(file_path)
    
    return processed_files, modified_files

def main():
    """主函数"""
    # 获取项目根目录
    if len(sys.argv) > 1:
        project_dir = sys.argv[1]
    else:
        project_dir = os.getcwd()
    
    if not os.path.exists(project_dir):
        print(f"错误：目录 {project_dir} 不存在")
        sys.exit(1)
    
    print(f"开始批量替换项目目录: {project_dir}")
    print("正在处理文件...")
    
    # 执行批量替换
    processed_files, modified_files = batch_replace_ajax_post(project_dir)
    
    # 输出结果
    print("=" * 60)
    print("批量替换完成！")
    print("=" * 60)
    print(f"处理的文件总数: {len(processed_files)}")
    print(f"修改的文件数量: {len(modified_files)}")
    
    if modified_files:
        print("\n修改的文件列表:")
        for file_path in modified_files:
            rel_path = os.path.relpath(file_path, project_dir)
            print(f"  ✓ {rel_path}")
    else:
        print("\n没有文件需要修改。")
    
    print("\n建议:")
    print("1. 运行验证脚本检查替换结果")
    print("2. 测试应用程序功能")
    print("3. 提交代码更改")

if __name__ == "__main__":
    main()
