# 双Token认证系统

本文档描述了NAStool项目中实现的双Token认证系统，该系统使用`access_token`和`refresh_token`来提供更安全和灵活的用户认证。

## 系统概述

### 传统单Token系统的问题
- 单一Token过期时间难以平衡安全性和用户体验
- Token泄露风险高，无法主动撤销
- 无法支持多设备登录管理

### 双Token系统的优势
- **Access Token**: 短期有效（2小时），用于API访问
- **Refresh Token**: 长期有效（30天），用于刷新Access Token
- 支持Token主动撤销和设备管理
- 更好的安全性和用户体验平衡

## 系统架构

### 后端组件

#### 1. 数据库模型
- `CONFIGUSERS`: 用户表，新增`LAST_PASSWORD_CHANGE`字段
- `CONFIGREFRESHTOKENS`: Refresh Token管理表

#### 2. 核心模块
- `web/security.py`: Token生成、验证和刷新逻辑
- `web/fastapi_api.py`: 认证相关API端点
- `web/fastapi_app.py`: 认证依赖和中间件

#### 3. API端点
- `POST /api/user/login`: 用户登录，返回双Token
- `POST /api/auth/refresh`: 刷新Access Token
- `POST /api/auth/logout`: 登出并撤销Refresh Token

### 前端组件

#### 1. 认证管理器
- `web/static/js/auth-manager.js`: Token存储和管理
- 自动Token刷新
- 登录状态检查

#### 2. Axios封装
- `web/static/js/axios-wrapper.js`: HTTP请求封装
- 自动添加Authorization头
- 401错误处理和Token刷新

## 使用指南

### 1. 数据库迁移

首先运行数据库迁移脚本：

```bash
python migration_add_refresh_token_support.py
```

### 2. 前端使用

#### 登录
```javascript
// 使用新的登录方法
const response = await login(username, password, remember);
```

#### API请求
```javascript
// 使用axios_post替代ajax_post（向后兼容）
axios_post('command', params, handler);

// 或使用新的api_request方法
const data = await api_request('/api/endpoint', requestData);
```

#### 登出
```javascript
// 使用新的登出方法
await logout();
```

### 3. 后端使用

#### Token生成
```python
from web.security import generate_access_token, generate_refresh_token

# 生成Access Token
access_token = generate_access_token(username)

# 生成Refresh Token
refresh_token = generate_refresh_token(user_id, device_info)
```

#### Token验证
```python
from web.security import verify_refresh_token, refresh_access_token

# 验证Refresh Token
is_valid, user_id = verify_refresh_token(refresh_token)

# 刷新Access Token
success, new_access_token, username = refresh_access_token(refresh_token)
```

#### Token撤销
```python
from web.security import revoke_refresh_token, revoke_user_refresh_tokens

# 撤销单个Refresh Token
revoke_refresh_token(refresh_token)

# 撤销用户所有Refresh Token（密码更改时）
revoke_user_refresh_tokens(user_id)
```

## 安全特性

### 1. Token管理
- Access Token短期有效，减少泄露风险
- Refresh Token存储在数据库中，支持主动撤销
- Token哈希存储，防止数据库泄露

### 2. 自动失效机制
- 密码更改时自动撤销所有Refresh Token
- Token过期自动清理
- 设备登出时撤销对应Token

### 3. 安全验证
- 密码更改时间检查
- Token有效性验证
- 401错误自动处理

## 测试

运行测试脚本验证系统功能：

```bash
python test_dual_token_auth.py [服务器地址]
```

测试包括：
- 用户登录
- 认证请求
- Token刷新
- 无效Token处理
- 用户登出

## 配置选项

### Token过期时间
在`web/security.py`中可以调整：
- Access Token: 默认2小时
- Refresh Token: 默认30天

### 前端存储
Token存储在localStorage中：
- `nas_access_token`: Access Token
- `nas_refresh_token`: Refresh Token
- `nas_user_info`: 用户信息

## 兼容性

### 向后兼容
- 保留原有的`ajax_post`方法
- 登录API同时返回新旧格式Token
- 支持渐进式迁移

### 前端迁移
建议逐步将`ajax_post`替换为`axios_post`：
1. 新功能使用`axios_post`
2. 现有功能逐步迁移
3. 最终移除`ajax_post`

## 故障排除

### 常见问题

1. **Token刷新失败**
   - 检查Refresh Token是否有效
   - 确认数据库连接正常
   - 验证用户密码未更改

2. **401错误持续出现**
   - 检查axios拦截器配置
   - 确认Token存储正常
   - 验证后端认证逻辑

3. **登录后立即退出**
   - 检查Token格式是否正确
   - 确认前端状态管理正常
   - 验证重定向逻辑

### 调试工具
- 浏览器开发者工具查看Network请求
- localStorage检查Token存储
- 后端日志查看认证过程

## 最佳实践

1. **定期清理过期Token**
2. **监控异常登录行为**
3. **及时更新安全配置**
4. **定期备份用户数据**

## 更新日志

- v1.0.0: 初始双Token认证系统实现
- 支持Access Token和Refresh Token
- 前端axios封装和自动刷新
- 数据库模型扩展和迁移脚本
