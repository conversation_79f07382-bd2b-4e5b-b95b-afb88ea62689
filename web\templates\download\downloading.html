{% import 'macro/svg.html' as SVG %}
{% import 'macro/oops.html' as OOPS %}

<style>
  .space-between-box a:hover {
    text-decoration: none;
  }
  div.top-sub-navbar>ul>li {
      margin: 0 1em;
  }
</style>
<div class="input-group top-sub-navbar" id="downloader-navbar">
  <input type="hidden" id="downloaderId" value="{{ DownloaderId }}">
  <ul class="nav nav-tabs card-header-tabs" data-bs-toggle="tabs" role="tablist">
    {% for dataItem in Downloaders %}
    <li class="nav-item">
      <a class="nav-link {% if dataItem.id == DownloaderId %} active {% endif %}" href="#" data-bs-toggle="tab" data-id="ranking" onclick="navmenu('downloading?downloaderId={{dataItem.id}}')">
        <span class="d-md-none" style="color:var(--tblr-body-color);">{{dataItem.name}}</span>
        <span class="d-none d-md-inline">{{dataItem.name}}</span>
      </a>
    </li>
    {% endfor %}
  </ul>
</div>
<div class="container-xl">
  <!-- Page title -->
  <div class="page-header d-print-none">
    <div class="row align-items-center">
      <div class="col">
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="btn-list">
          <button data-bs-toggle="dropdown" type="button"
            class="btn btn-primary d-none d-sm-inline-block dropdown-toggle">
            {{ SVG.plus() }}
            新增下载
          </button>
          <button data-bs-toggle="dropdown" type="button" class="btn btn-primary d-sm-none btn-icon">
            {{ SVG.plus() }}
          </button>
          <div class="dropdown-menu dropdown-menu-end">
            <button class="dropdown-item" onclick="show_torrent_download_modal('torrent')">
              种子文件
            </button>
            <button class="dropdown-item" onclick="show_torrent_download_modal('url')">
              种子链接
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% if DownloadCount > 0 %}
<div class="page-body">
  <div class="container-xl">
    <div class="d-grid gap-3 grid-info-card">
      {% for Torrent in Torrents %}
      <div class="card p-0">
        <div class="card-body media-box" style="background-image: url({{ Torrent.backdrop }});">
          {% if Torrent.vote %}
          <div class="ribbon ribbon-top ribbon-bookmark bg-purple">{{Torrent.vote}}</div>
          {% endif %}
          <div class="row align-items-stretch media-content card-text">
            {% if Torrent.image %}
            <div class="col-auto p-0 card">
              <img src="{{ Torrent.image }}" class="rounded" alt="" style="max-width: 80px;">
            </div>
            {% endif %}
            <div class="col">
              <h3 class="card-title mb-1">
                <a href='javascript:navmenu("media_detail?type={{ Torrent.type }}&id={{ Torrent.tmdbid }}")'
                  class="text-reset card-text">{{
                  Torrent.title }}</a>
              </h3>
              <div id="speed_text_{{ Torrent.id }}">
                {{ Torrent.speed }}
              </div>
              <div class="mt-4">
                <div class="space-between-box">
                  <div class="col-auto" id="progress_text_{{ Torrent.id }}">
                    {{ Torrent.sizeprogress }}
                  </div>
                  <div class="col-auto">
                    <a href="javascript:start_pt_download('{{ Torrent.id }}')" id="start_btn_{{ Torrent.id }}"
                      style="{% if Torrent.state == 'Downloading' %}display:none{% endif %}">{{
                      SVG.player_play() }}</a>
                    <a href="javascript:stop_pt_download('{{ Torrent.id }}')" id="stop_btn_{{ Torrent.id }}"
                      style="{% if Torrent.state == 'Stoped' %}display:none{% endif %}">{{
                      SVG.player_pause() }}</a>
                    <a href="javascript:remove_pt_download('{{ Torrent.id }}', '{{ Torrent.title }}')"
                      class="text-danger">{{
                      SVG.x() }}</a>
                  </div>
                </div>
              </div>
              <div class="progress progress-sm progress-bottom">
                <div class="progress-bar" id="progress_{{ Torrent.id }}" style="width: {{ Torrent.progress }}%"
                  role="progressbar" aria-valuenow="{{ Torrent.progress }}" aria-valuemin="0" aria-valuemax="100">
                  <span class="visually-hidden"></span>
                </div>
              </div>
            </div>
            <input type="hidden" class="download_ids" id="id_{{ Torrent.id }}" value="{{ Torrent.id }}">
          </div>
        </div>
      </div>
      {% endfor %}
    </div>
  </div>
</div>
{% else %}
{{ OOPS.nodatafound('没有下载任务', '当前下载器中没有正在下载的任务。') }}
{% endif %}
<script type="text/javascript">

  $('#downloader-navbar').show();

  // 下载控制
  function start_pt_download(id) {
    axios_post("pt_start", { "id": id }, function (ret) {
      get_torrent_info(ret.id)
    });
  }

  function stop_pt_download(id) {
    axios_post("pt_stop", { "id": id }, function (ret) {
      get_torrent_info(ret.id)
    });
  }

  function remove_pt_download(id, download_name) {

    show_ask_modal("是否确定删除： " + download_name + " 下载任务?", function () {
      // 隐藏询问框
      hide_ask_modal();
      // 执行删除
      axios_post("pt_remove", { "id": id }, function (ret) {
        window_history_refresh();
      });
    }, '');

  }

  //更新所有种子页面信息
  function get_all_torrents_info() {
    let ids = [];
    $(".download_ids").each(function () {
      ids.push($(this).val());
    });
    if (ids.length == 0) {
      return;
    }
    let downloaderId = $('#downloaderId').val();
    axios_post("pt_info", { "downloaderId": downloaderId, "ids": ids }, function (ret) {
      if (ret.retcode == "0") {
        for (let torrent of ret.torrents) {
          update_torrent_ui(torrent);
        }
        setTimeout("get_all_torrents_info()", 2000);
      }
    }, true, false);
  }

  //更新一个种子页面信息
  function get_torrent_info(id) {
    axios_post("pt_info", { "ids": id }, function (ret) {
      if (ret.retcode == "0") {
        for (let torrent of ret.torrents) {
          update_torrent_ui(torrent);
        }
      }
    }, true, false);
  }

  //更新单个种子的页面信息
  function update_torrent_ui(info) {
    $(`#speed_text_${info.id}`).text(info.speed);
    $(`#progress_text_${info.id}`).text(`${info.sizeprogress}`);
    $(`#progress_${info.id}`).attr("style", `width: ${info.progress}%`)
      .attr("aria-valuenow", info.progress);
    if (info.state === "Stoped") {
      $(`#start_btn_${info.id}`).show()
      $(`#stop_btn_${info.id}`).hide()
    } else {
      $(`#start_btn_${info.id}`).hide()
      $(`#stop_btn_${info.id}`).show()
    }
  }

  //显示新增下载界面
  function show_torrent_download_modal(type) {
    show_download_modal('', '', '', function () {
      let urls = $("#torrent_urls").val();
      if ((!TorrentDropZone || !TorrentDropZone.files || !TorrentDropZone.files[0]) && !urls) {
        return;
      }
      $("#search_download_btn").text("处理中...").attr("disabled", true);
      const params = {
        "files": TorrentDropZone.files,
        "urls": urls.split("\n"),
        "dl_dir": get_savepath("search_download_dir", "search_download_dir_manual"),
        "dl_setting": $("#search_download_setting").val()
      }
      axios_post("download_torrent", params, function (ret) {
        $("#search_download_btn").attr("disabled", false).text("下载");
        $("#modal-search-download").modal('hide');
        if (ret.code == 0) {
          TorrentDropZone.removeAllFiles();
          $("#torrent_urls").val("");
          window_history_refresh();
        } else {
          show_fail_modal(`添加下载失败：${ret.msg}`, function () {
            $("#modal-search-download").modal('show');
          });
        }
      });
    }, type);
  }

  //事件
  setTimeout("get_all_torrents_info()", 2000);

</script>