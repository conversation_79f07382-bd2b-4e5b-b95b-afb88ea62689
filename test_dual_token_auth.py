#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
双Token认证系统测试脚本
测试登录、Token刷新、登出等功能
"""

import requests
import json
import time
import sys

class DualTokenAuthTester:
    def __init__(self, base_url="http://localhost:3000"):
        self.base_url = base_url
        self.access_token = None
        self.refresh_token = None
        self.session = requests.Session()
    
    def test_login(self, username="admin", password="password"):
        """测试登录功能"""
        print("=== 测试登录功能 ===")
        
        url = f"{self.base_url}/api/user/login"
        data = {
            "username": username,
            "password": password,
            "remember": True
        }
        
        try:
            response = self.session.post(url, json=data)
            print(f"登录请求状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"登录响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                if result.get('success') and result.get('data'):
                    self.access_token = result['data'].get('access_token')
                    self.refresh_token = result['data'].get('refresh_token')
                    print("✓ 登录成功")
                    print(f"Access Token: {self.access_token[:50]}...")
                    print(f"Refresh Token: {self.refresh_token[:50]}...")
                    return True
                else:
                    print(f"✗ 登录失败: {result.get('message')}")
                    return False
            else:
                print(f"✗ 登录请求失败: {response.text}")
                return False
                
        except Exception as e:
            print(f"✗ 登录异常: {e}")
            return False
    
    def test_authenticated_request(self):
        """测试需要认证的请求"""
        print("\n=== 测试认证请求 ===")
        
        if not self.access_token:
            print("✗ 没有Access Token，跳过测试")
            return False
        
        url = f"{self.base_url}/api/user/info"
        headers = {
            "Authorization": f"Bearer {self.access_token}"
        }
        
        try:
            response = self.session.get(url, headers=headers)
            print(f"认证请求状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"用户信息: {json.dumps(result, indent=2, ensure_ascii=False)}")
                print("✓ 认证请求成功")
                return True
            else:
                print(f"✗ 认证请求失败: {response.text}")
                return False
                
        except Exception as e:
            print(f"✗ 认证请求异常: {e}")
            return False
    
    def test_token_refresh(self):
        """测试Token刷新功能"""
        print("\n=== 测试Token刷新 ===")
        
        if not self.refresh_token:
            print("✗ 没有Refresh Token，跳过测试")
            return False
        
        url = f"{self.base_url}/api/auth/refresh"
        data = {
            "refresh_token": self.refresh_token
        }
        
        try:
            response = self.session.post(url, json=data)
            print(f"刷新请求状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"刷新响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                if result.get('success') and result.get('data'):
                    new_access_token = result['data'].get('access_token')
                    print(f"新Access Token: {new_access_token[:50]}...")
                    print("✓ Token刷新成功")
                    
                    # 更新Access Token
                    old_token = self.access_token
                    self.access_token = new_access_token
                    
                    # 验证新Token是否有效
                    if self.test_authenticated_request():
                        print("✓ 新Token验证成功")
                        return True
                    else:
                        print("✗ 新Token验证失败")
                        self.access_token = old_token
                        return False
                else:
                    print(f"✗ Token刷新失败: {result.get('message')}")
                    return False
            else:
                print(f"✗ 刷新请求失败: {response.text}")
                return False
                
        except Exception as e:
            print(f"✗ Token刷新异常: {e}")
            return False
    
    def test_invalid_token_handling(self):
        """测试无效Token处理"""
        print("\n=== 测试无效Token处理 ===")
        
        # 使用无效的Access Token
        invalid_token = "invalid.token.here"
        url = f"{self.base_url}/api/user/info"
        headers = {
            "Authorization": f"Bearer {invalid_token}"
        }
        
        try:
            response = self.session.get(url, headers=headers)
            print(f"无效Token请求状态码: {response.status_code}")
            
            if response.status_code == 401:
                print("✓ 无效Token正确返回401")
                return True
            else:
                print(f"✗ 无效Token处理异常，期望401，实际{response.status_code}")
                return False
                
        except Exception as e:
            print(f"✗ 无效Token测试异常: {e}")
            return False
    
    def test_logout(self):
        """测试登出功能"""
        print("\n=== 测试登出功能 ===")
        
        if not self.refresh_token:
            print("✗ 没有Refresh Token，跳过测试")
            return False
        
        url = f"{self.base_url}/api/auth/logout"
        data = {
            "refresh_token": self.refresh_token
        }
        
        try:
            response = self.session.post(url, json=data)
            print(f"登出请求状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"登出响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
                print("✓ 登出成功")
                
                # 验证Refresh Token是否已失效
                print("验证Refresh Token是否已失效...")
                refresh_result = self.test_token_refresh()
                if not refresh_result:
                    print("✓ Refresh Token已正确失效")
                    return True
                else:
                    print("✗ Refresh Token未失效")
                    return False
            else:
                print(f"✗ 登出请求失败: {response.text}")
                return False
                
        except Exception as e:
            print(f"✗ 登出异常: {e}")
            return False
    
    def run_all_tests(self, username="admin", password="password"):
        """运行所有测试"""
        print("开始双Token认证系统测试...\n")
        
        results = []
        
        # 测试登录
        results.append(("登录", self.test_login(username, password)))
        
        # 测试认证请求
        results.append(("认证请求", self.test_authenticated_request()))
        
        # 测试Token刷新
        results.append(("Token刷新", self.test_token_refresh()))
        
        # 测试无效Token处理
        results.append(("无效Token处理", self.test_invalid_token_handling()))
        
        # 测试登出
        results.append(("登出", self.test_logout()))
        
        # 输出测试结果
        print("\n" + "="*50)
        print("测试结果汇总:")
        print("="*50)
        
        passed = 0
        total = len(results)
        
        for test_name, result in results:
            status = "✓ 通过" if result else "✗ 失败"
            print(f"{test_name:<15} {status}")
            if result:
                passed += 1
        
        print("="*50)
        print(f"总计: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有测试通过！")
            return True
        else:
            print("❌ 部分测试失败，请检查系统配置")
            return False

def main():
    """主函数"""
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = "http://localhost:3000"
    
    print(f"测试服务器: {base_url}")
    
    # 获取用户名和密码
    username = input("请输入用户名 (默认: admin): ").strip() or "admin"
    password = input("请输入密码 (默认: password): ").strip() or "password"
    
    tester = DualTokenAuthTester(base_url)
    success = tester.run_all_tests(username, password)
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
