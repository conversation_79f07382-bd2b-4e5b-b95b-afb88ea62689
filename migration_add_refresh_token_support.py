#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据库迁移脚本：添加双Token认证支持
- 为CONFIGUSERS表添加LAST_PASSWORD_CHANGE字段
- 创建CONFIGREFRESHTOKENS表
"""

import sqlite3
import os
import sys
from datetime import datetime

def get_db_path():
    """获取数据库路径"""
    # 尝试从配置中获取数据库路径
    try:
        from config import Config
        config = Config()
        db_path = config.get_config_path() + "/user.db"
        if os.path.exists(db_path):
            return db_path
    except:
        pass
    
    # 默认路径
    default_paths = [
        "config/user.db",
        "../config/user.db",
        "user.db"
    ]
    
    for path in default_paths:
        if os.path.exists(path):
            return path
    
    print("错误：找不到数据库文件")
    return None

def backup_database(db_path):
    """备份数据库"""
    backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    try:
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"数据库已备份到: {backup_path}")
        return backup_path
    except Exception as e:
        print(f"备份数据库失败: {e}")
        return None

def check_table_exists(cursor, table_name):
    """检查表是否存在"""
    cursor.execute("""
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name=?
    """, (table_name,))
    return cursor.fetchone() is not None

def check_column_exists(cursor, table_name, column_name):
    """检查列是否存在"""
    cursor.execute(f"PRAGMA table_info({table_name})")
    columns = [row[1] for row in cursor.fetchall()]
    return column_name in columns

def migrate_database(db_path):
    """执行数据库迁移"""
    print(f"开始迁移数据库: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 检查并添加LAST_PASSWORD_CHANGE字段到CONFIGUSERS表
        if check_table_exists(cursor, 'CONFIG_USERS'):
            if not check_column_exists(cursor, 'CONFIG_USERS', 'LAST_PASSWORD_CHANGE'):
                print("添加LAST_PASSWORD_CHANGE字段到CONFIG_USERS表...")
                cursor.execute("""
                    ALTER TABLE CONFIG_USERS 
                    ADD COLUMN LAST_PASSWORD_CHANGE TEXT
                """)
                print("✓ LAST_PASSWORD_CHANGE字段添加成功")
            else:
                print("✓ LAST_PASSWORD_CHANGE字段已存在")
        else:
            print("警告：CONFIG_USERS表不存在")
        
        # 2. 创建CONFIGREFRESHTOKENS表
        if not check_table_exists(cursor, 'CONFIG_REFRESH_TOKENS'):
            print("创建CONFIG_REFRESH_TOKENS表...")
            cursor.execute("""
                CREATE TABLE CONFIG_REFRESH_TOKENS (
                    ID INTEGER PRIMARY KEY AUTOINCREMENT,
                    USER_ID INTEGER,
                    TOKEN_HASH TEXT,
                    DEVICE_INFO TEXT,
                    CREATED_AT TEXT,
                    EXPIRES_AT TEXT,
                    IS_ACTIVE INTEGER DEFAULT 1,
                    LAST_USED TEXT
                )
            """)
            
            # 创建索引
            cursor.execute("""
                CREATE INDEX idx_refresh_tokens_user_id 
                ON CONFIG_REFRESH_TOKENS(USER_ID)
            """)
            
            cursor.execute("""
                CREATE INDEX idx_refresh_tokens_token_hash 
                ON CONFIG_REFRESH_TOKENS(TOKEN_HASH)
            """)
            
            print("✓ CONFIG_REFRESH_TOKENS表创建成功")
        else:
            print("✓ CONFIG_REFRESH_TOKENS表已存在")
        
        # 提交更改
        conn.commit()
        print("✓ 数据库迁移完成")
        
    except Exception as e:
        print(f"迁移失败: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()
    
    return True

def main():
    """主函数"""
    print("=== 双Token认证系统数据库迁移 ===")
    
    # 获取数据库路径
    db_path = get_db_path()
    if not db_path:
        sys.exit(1)
    
    print(f"数据库路径: {db_path}")
    
    # 备份数据库
    backup_path = backup_database(db_path)
    if not backup_path:
        print("警告：数据库备份失败，继续执行可能有风险")
        response = input("是否继续？(y/N): ")
        if response.lower() != 'y':
            sys.exit(1)
    
    # 执行迁移
    if migrate_database(db_path):
        print("\n✓ 迁移成功完成！")
        print("\n注意事项：")
        print("1. 重启应用程序以使更改生效")
        print("2. 用户需要重新登录以获取新的双Token")
        print("3. 如果遇到问题，可以使用备份文件恢复数据库")
        if backup_path:
            print(f"   备份文件: {backup_path}")
    else:
        print("\n✗ 迁移失败！")
        if backup_path:
            print(f"可以使用备份文件恢复: {backup_path}")
        sys.exit(1)

if __name__ == "__main__":
    main()
