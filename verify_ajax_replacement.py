#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证ajax_post替换为axios_post的完整性
检查项目中是否还有遗漏的ajax_post调用
"""

import os
import re
import sys
from pathlib import Path

def find_ajax_post_calls(directory):
    """查找目录中所有ajax_post调用"""
    ajax_calls = []
    
    # 需要检查的文件扩展名
    extensions = ['.js', '.html', '.htm']
    
    # 排除的目录
    exclude_dirs = {
        '__pycache__', 
        '.git', 
        'node_modules', 
        '.vscode',
        'venv',
        'env'
    }
    
    # 排除的文件（保留原始ajax_post定义）
    exclude_files = {
        'util.js'  # 保留原始ajax_post函数定义
    }
    
    for root, dirs, files in os.walk(directory):
        # 排除特定目录
        dirs[:] = [d for d in dirs if d not in exclude_dirs]
        
        for file in files:
            if any(file.endswith(ext) for ext in extensions):
                if file in exclude_files:
                    continue
                    
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        
                    # 查找ajax_post调用（排除函数定义和注释）
                    lines = content.split('\n')
                    for line_num, line in enumerate(lines, 1):
                        # 跳过注释行
                        if line.strip().startswith('//') or line.strip().startswith('*') or line.strip().startswith('/*'):
                            continue
                            
                        # 查找ajax_post调用
                        if 'ajax_post(' in line:
                            # 排除函数定义
                            if 'function ajax_post(' not in line:
                                ajax_calls.append({
                                    'file': file_path,
                                    'line': line_num,
                                    'content': line.strip()
                                })
                                
                except Exception as e:
                    print(f"警告：无法读取文件 {file_path}: {e}")
    
    return ajax_calls

def check_axios_post_availability(directory):
    """检查axios_post是否可用"""
    issues = []
    
    # 检查axios-wrapper.js是否存在
    axios_wrapper_path = os.path.join(directory, 'web', 'static', 'js', 'axios-wrapper.js')
    if not os.path.exists(axios_wrapper_path):
        issues.append("axios-wrapper.js文件不存在")
    
    # 检查auth-manager.js是否存在
    auth_manager_path = os.path.join(directory, 'web', 'static', 'js', 'auth-manager.js')
    if not os.path.exists(auth_manager_path):
        issues.append("auth-manager.js文件不存在")
    
    # 检查navigation.html是否引入了必要的脚本
    nav_template_path = os.path.join(directory, 'web', 'templates', 'navigation.html')
    if os.path.exists(nav_template_path):
        try:
            with open(nav_template_path, 'r', encoding='utf-8') as f:
                nav_content = f.read()
                
            if 'axios.min.js' not in nav_content:
                issues.append("navigation.html中未引入axios库")
            if 'auth-manager.js' not in nav_content:
                issues.append("navigation.html中未引入auth-manager.js")
            if 'axios-wrapper.js' not in nav_content:
                issues.append("navigation.html中未引入axios-wrapper.js")
                
        except Exception as e:
            issues.append(f"无法检查navigation.html: {e}")
    else:
        issues.append("navigation.html文件不存在")
    
    return issues

def generate_report(ajax_calls, axios_issues):
    """生成验证报告"""
    print("=" * 60)
    print("AJAX_POST 替换验证报告")
    print("=" * 60)
    
    if not ajax_calls and not axios_issues:
        print("✅ 验证通过！")
        print("- 所有ajax_post调用已成功替换为axios_post")
        print("- axios_post相关文件配置正确")
        return True
    
    success = True
    
    if ajax_calls:
        print(f"❌ 发现 {len(ajax_calls)} 个未替换的ajax_post调用：")
        print("-" * 40)
        
        # 按文件分组显示
        files_with_calls = {}
        for call in ajax_calls:
            file_path = call['file']
            if file_path not in files_with_calls:
                files_with_calls[file_path] = []
            files_with_calls[file_path].append(call)
        
        for file_path, calls in files_with_calls.items():
            print(f"\n📁 {file_path}")
            for call in calls:
                print(f"   第{call['line']}行: {call['content']}")
        
        success = False
    
    if axios_issues:
        print(f"\n❌ axios_post配置问题：")
        print("-" * 40)
        for issue in axios_issues:
            print(f"   • {issue}")
        success = False
    
    if not success:
        print("\n" + "=" * 60)
        print("修复建议：")
        print("1. 将剩余的ajax_post调用替换为axios_post")
        print("2. 确保所有必要的JavaScript文件已正确引入")
        print("3. 运行数据库迁移脚本")
        print("4. 重新启动应用程序")
    
    return success

def main():
    """主函数"""
    # 获取项目根目录
    if len(sys.argv) > 1:
        project_dir = sys.argv[1]
    else:
        project_dir = os.getcwd()
    
    if not os.path.exists(project_dir):
        print(f"错误：目录 {project_dir} 不存在")
        sys.exit(1)
    
    print(f"正在验证项目目录: {project_dir}")
    print("正在扫描ajax_post调用...")
    
    # 查找ajax_post调用
    ajax_calls = find_ajax_post_calls(project_dir)
    
    print("正在检查axios_post配置...")
    
    # 检查axios_post可用性
    axios_issues = check_axios_post_availability(project_dir)
    
    # 生成报告
    success = generate_report(ajax_calls, axios_issues)
    
    # 生成详细的替换统计
    if ajax_calls:
        print(f"\n📊 统计信息：")
        js_files = sum(1 for call in ajax_calls if call['file'].endswith('.js'))
        html_files = sum(1 for call in ajax_calls if call['file'].endswith(('.html', '.htm')))
        print(f"   JavaScript文件: {js_files} 个调用")
        print(f"   HTML模板文件: {html_files} 个调用")
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
