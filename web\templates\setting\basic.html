{% import 'macro/svg.html' as SVG %}
{% import 'macro/form.html' as FORM %}

<style>
  .card {
    border: none;
  }

  .card-body {
    padding: var(--tblr-card-spacer-x) var(--tblr-card-spacer-x);
    border-bottom: var(--tblr-card-border-width) solid var(--tblr-card-border-color);
  }

  .form-group {
    margin: 10px 0 10px 0;
  }

  .control-label {
    flex: 0 0 auto;
    width: 10%;
  }

  div.form-group>div>input {
    width: 50%;
    margin: 0 10px 0 10px;
  }
</style>

<!-- 业务页面代码 -->
<div class="card components-div" id="sys_config">
  <div class="card">
    <div class="card-header" style="display: flex; align-items: center;">
      <h4 class="card-title"><strong>基础设置</strong></h4>
      <a href="javascript:void(0)" onclick="slideToggle('basic_system')" class="btn-action ms-1" title="展开/折叠">
        {{ SVG.menu_2() }}
      </a>
      <div class="col-auto" style="margin-left: auto;">
        <a href="javascript:void(0)" class="btn d-none d-sm-inline-block" data-bs-toggle="modal"
          data-bs-target="#modal-user-script">
          自定义 CSS/JavaScript
        </a>
        <a href="javascript:void(0)" class="btn d-sm-none btn-icon" data-bs-toggle="modal"
          data-bs-target="#modal-user-script" title="自定义CSS/JavaScript">
          {{ SVG.code_dots() }}
        </a>
      </div>
    </div>
    <div class="card-body" id="basic_system">
      <div class="row">
        <div class="col-lg">
          <div class="mb-3">
            <label class="form-label required">WEB服务端口</label>
            <input type="text" value="{{ Config.app.web_port or '' }}" class="form-control" id="app.web_port"
              placeholder="3000" autocomplete="off">
          </div>
        </div>
        <div class="col-lg">
          <div class="mb-3">
            <label class="form-label required">WEB管理用户</label>
            <input type="text" value="{{ Config.app.login_user or '' }}" class="form-control" id="app.login_user"
              placeholder="admin" autocomplete="off">
          </div>
        </div>
        <div class="col-lg">
          <div class="mb-3">
            <label class="form-label required">WEB管理密码</label>
            <input type="password" value="{{ Config.app.login_password or '' }}" class="form-control"
              id="app.login_password" placeholder="password" autocomplete="off">
          </div>
        </div>
        <div class="col-lg">
          <div class="mb-3">
            <label class="form-label required">WEB壁纸来源 <span class="form-help"
                title="登录界面壁纸来源：TMDB、Bing，设置为TMDB时需在媒体配置TMDB API Key" data-bs-toggle="tooltip">?</span></label>
            <select class="form-select" id="app.wallpaper">
              <option value="themoviedb" {% if not Config.app.wallpaper or Config.app.wallpaper=='themoviedb'
                %}selected{% endif %}>
                电影海报
              </option>
              <option value="bing" {% if Config.app.wallpaper=='bing' %}selected{% endif %}>
                Bing每日壁纸
              </option>
            </select>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-lg">
          <div class="mb-3">
            <label class="form-label">日志级别 <span class="form-help" title="一般情况下请选择INFO，排查问题可选择DEBUG"
                data-bs-toggle="tooltip">?</span></label>
            <select class="form-select" id="app.loglevel">
              <option value="info" {% if not Config.app.loglevel or Config.app.loglevel=='info' %}selected{% endif %}>
                INFO
              </option>
              <option value="debug" {% if Config.app.loglevel=='debug' %}selected{% endif %}>
                DEBUG
              </option>
              <option value="error" {% if Config.app.loglevel=='error' %}selected{% endif %}>
                ERROR
              </option>
            </select>
          </div>
        </div>
        <div class="col-lg">
          <div class="mb-3">
            <label class="form-label required">日志输出类型</label>
            <select class="form-select" id="app.logtype">
              <option value="console" {% if Config.app.logtype=='console' %}selected{% endif %}>控制台</option>
              <option value="file" {% if Config.app.logtype=='file' %}selected{% endif %}>文件</option>
              <option value="server" {% if Config.app.logtype=='server' %}selected{% endif %}>日志中心</option>
            </select>
          </div>
        </div>
        <div class="col-lg">
          <div class="mb-3">
            <label class="form-label">日志文件路径 <span class="form-help" title="日志输出类型为文件时需要配置该项"
                data-bs-toggle="tooltip">?</span></label>
            <input type="text" value="{{ Config.app.logpath or '' }}" class="form-control filetree-folders-only"
              id="app.logpath" placeholder="/config/logs" autocomplete="off">
          </div>
        </div>
        <div class="col-lg">
          <div class="mb-3">
            <label class="form-label">日志中心地址 <span class="form-help"
                title="日志输出类型为日志中心时需要配置该项；需要配置IP地址和端口，配置示例：127.0.0.1:514" data-bs-toggle="tooltip">?</span></label>
            <input type="text" value="{{ Config.app.logserver or '' }}" class="form-control" id="app.logserver"
              placeholder="127.0.0.1:514" autocomplete="off">
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-12 col-xl-3">
          <div class="mb-3">
            <label class="form-label required">TMDB API Key <span class="form-help"
                title="在themoviedb.org网站申请API Key，该项必须配置，否则所有功能无法正常运行" data-bs-toggle="tooltip">?</span></label>
            <input type="text" value="{{ Config.app.rmt_tmdbkey or '' }}"
              class="form-control {% if not Config.app.rmt_tmdbkey %}is-invalid{% endif %}" id="app.rmt_tmdbkey"
              placeholder="" autocomplete="off">
          </div>
        </div>
        <div class="col-12 col-xl-3">
          <div class="mb-3">
            <label class="form-label required">TMDB API Url <span class="form-help"
                title="选择访问TMDB API时使用的地址，api.themoviedb.org、api.tmdb.org为官方地址，其余为使用代理中转，如无法连接TMDB匹配媒体信息时可偿试更换"
                data-bs-toggle="tooltip">?</span></label>
            <select class="form-select" id="app.tmdb_domain">
              {% for domain in TmdbDomains %}
              <option value="{{ domain }}" {% if Config.app.tmdb_domain==domain %}selected{% endif %}>
                {{ domain }}
              </option>
              {% endfor %}
            </select>
          </div>
        </div>
        <div class="col-12 col-xl-1">
          <div class="mb-3">
            <label class="form-label">TMDB语言 <span class="form-help"
                title="选择TMDB语言，将会影响媒体信息展示、文件整理及刮削等所有涉及使用TMDB数据的内容语言" data-bs-toggle="tooltip">?</span></label>
            <select class="form-select" id="media.tmdb_language">
              <option value="zh" {% if not Config.media.tmdb_language or Config.media.tmdb_language=='zh' %}selected{%
                endif %}>中文</option>
              <option value="en" {% if Config.media.tmdb_language=='en' %}selected{% endif %}>English</option>
            </select>
          </div>
        </div>
        <div class="col-12 col-xl-2">
          <div class="mb-3">
            <label class="form-label required">TMDB匹配模式 <span class="form-help"
                title="正常模式下会提升识别成功率，但也可能会导致误识别率增加；严格模式可以降低误识别率，但可能导致很多文件名/种子名中年份不正确的无法被识别（特别是剧集，需要是首播年份）"
                data-bs-toggle="tooltip">?</span></label>
            <select class="form-select" id="app.rmt_match_mode">
              <option value="normal" {% if Config.app.rmt_match_mode=='normal' %}selected{% endif %}>正常模式
              </option>
              <option value="strict" {% if Config.app.rmt_match_mode=='strict' %}selected{% endif %}>严格模式
              </option>
            </select>
          </div>
        </div>
        <div class="col-12 col-xl-3">
          <div class="mb-3">
            <label class="form-label">TMDB图片代理 <span class="form-help" title="加速TMDB图片下载，留空使用TMDB官方地址"
                data-bs-toggle="tooltip">?</span></label>
            <input type="text" value="{% if Config.app %}{{ Config.app.tmdb_image_url or '' }}{% endif %}"
              class="form-control" id="app.tmdb_image_url" placeholder="https://image.tmdb.org" autocomplete="off">
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-12 col-xl-3">
          <div class="mb-3">
            <label class="form-label">代理服务器 <span class="form-help"
                title="将使用代理访服务器访问themoviedb、telegram等境外网站及程序更新(git)，站点默认不使用代理，如需使用需在站点维护中开启；配置格式示例：127.0.0.1:7890（Http协议）、socks5://127.0.0.1:8018、socks5h://127.0.0.1:8018(remote DNS)"
                data-bs-toggle="tooltip">?</span></label>
            <input type="text" value="{{ Proxy or '' }}" class="form-control" id="app.proxies"
              placeholder="127.0.0.1:7890" autocomplete="off">
          </div>
        </div>
        <div class="col-12 col-xl-3">
          <div class="mb-6">
            <label class="form-label">自建OCR服务 <span class="form-help" title="填写后将使用自建的OCR服务进行验证码识别"
                data-bs-toggle="tooltip">?</span></label>
            <div class="input-group input-group-flat">
              <input type="text" value="{{ Config.app.ocr_url or '' }}" class="form-control" id="app.ocr_url"
                placeholder="http://127.0.0.1:9899/captcha/base64" autocomplete="off">
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-12 col-xl-3">
          <div class="mb-3">
            <label class="form-label">HTTPS证书文件路径 <span class="form-help" title="需要是pem格式的证书文件"
                data-bs-toggle="tooltip">?</span></label>
            <input type="text" value="{{ Config.app.ssl_cert or '' }}" class="form-control filetree-files-only"
              id="app.ssl_cert" placeholder="" autocomplete="off">
          </div>
        </div>
        <div class="col-12 col-xl-3">
          <div class="mb-3">
            <label class="form-label">HTTPS证书密钥文件路径</label>
            <input type="text" value="{{ Config.app.ssl_key or '' }}" class="form-control filetree-files-only"
              id="app.ssl_key" placeholder="" autocomplete="off">
          </div>
        </div>
        <div class="col-12 col-xl-3">
          <div class="mb-3">
            <label class="form-label">外网访问地址 <span class="form-help"
                title="使用该地址进行通知点击跳转以及设置Telegram机器人Webhook；需要配置IP地址和端口，如为https则需要加https://前缀；如启用Telegram机器人Webhook，则端口必须为：443, 80, 88, 8443之一，80、443一般运营商会封禁，建议使用88、8443"
                data-bs-toggle="tooltip">?</span></label>
            <input type="text" value="{{ Config.app.domain or '' }}" class="form-control" id="app.domain"
              placeholder="http://IP:PORT" autocomplete="off">
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-lg">
          <div class="mb-3">
            <label class="form-label">User-Agent <span class="form-help" title="如发现被豆瓣、PT站等封堵，请适当修改此项"
                data-bs-toggle="tooltip">?</span></label>
            <div class="input-group input-group-flat">
              <input type="text" value="{{ Config.app.user_agent or '' }}" class="form-control" id="app.user_agent"
                placeholder="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36"
                autocomplete="off">
              <span class="input-group-text">
                <a href="javascript:set_user_agent('app\\.user_agent')" class="link-secondary"
                  title="从浏览器中获取 User-Agent" data-bs-toggle="tooltip">
                  {{ SVG.arrow_bar_to_left() }}
                </a>
              </span>
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-lg">
          <div class="mb-3">
            <label class="form-check form-switch">
              <input class="form-check-input" type="checkbox" id="app.releases_update_only" {% if
                Config.app.releases_update_only %}checked{% endif %}>
              <span class="form-check-label">仅检查Releases更新 <span class="form-help" title="开启后，只有Releases更新，才会有更新提示"
                  data-bs-toggle="tooltip">?</span></span>
            </label>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="card">
    <div class="card-header" style="display: flex; align-items: center;">
      <h4 class="card-title"><strong>媒体设置</strong></h4>
      <a href="javascript:void(0)" onclick="slideToggle('media_param_setting')" class="btn-action ms-1" title="展开/折叠">
        {{ SVG.menu_2() }}
      </a>
      <div class="col-auto" style="margin-left: auto;">
        <a href="javascript:void(0)" class="btn d-none d-sm-inline-block" data-bs-toggle="modal"
          data-bs-target="#modal-scraper">
          刮削设置
        </a>
        <a href="javascript:void(0)" class="btn d-sm-none btn-icon" data-bs-toggle="modal"
          data-bs-target="#modal-scraper" title="刮削设置">
          {{ SVG.slideshow() }}
        </a>
      </div>
    </div>
    <div class="card-body" id="media_param_setting" style="display: none;">
      <div class="row">
        <div class="col-12 col-xl-3">
          <div class="mb-3">
            <label class="form-label">媒体服务器同步周期(小时) <span class="form-help" title="定时同步媒体服务器数据到本地，用于展示媒体是否存在，留空则不启用同步服务"
                data-bs-toggle="tooltip">?</span></label>
            <input type="text" value="{{ Config.media.mediasync_interval or '' }}" class="form-control"
              id="media.mediasync_interval" placeholder="留空关闭媒体服务器同步" autocomplete="off">
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-lg">
          <div class="mb-3">
            <label class="form-label">文件管理默认路径 <span class="form-help" title="文件管理、自定义识别等默认路径"
                data-bs-toggle="tooltip">?</span></label>
            <input type="text" value="{{ Config.media.media_default_path or '' }}" class="form-control"
              id="media.media_default_path" placeholder="/" autocomplete="off">
          </div>
        </div>
        <div class="col-lg">
          <div class="mb-3">
            <label class="form-label">文件二级分类策略 <span class="form-help"
                title="启用二级分类后会在电影/电视剧/动漫媒体库目录下按二级分类名建立子目录；此处配置分类的策略名，保存后会自动生成默认配置；如不需要启动分类，则该项配置为空"
                data-bs-toggle="tooltip">?</span></label>
            <div class="input-group input-group-flat">
              <input type="text" value="{{ Config.media.category or '' }}" class="form-control" id="media.category"
                placeholder="留空不启用二级分类" autocomplete="off" name="category_name">
              <span class="input-group-text">
                <a href="javascript:show_category_config_modal()" class="link-secondary" title="编辑二级分类策略"
                  data-bs-toggle="tooltip">
                  {{ SVG.edit() }}
                </a>
              </span>
            </div>
          </div>
        </div>
        <div class="col-lg">
          <div class="mb-3">
            <label class="form-label required">默认文件转移方式 <span class="form-help" title="手动识别等未指定转移方式的场景默认使用的转移方式"
                data-bs-toggle="tooltip">?</span></label>
            <select id="media.default_rmt_mode" class="form-select">
              {% for mode in RmtModeDict %}
              <option value="{{ mode.value }}" {% if Config.media.default_rmt_mode==mode.value %}selected{% endif %}>
                {{ mode.name }}</option>
              {% endfor %}
            </select>
          </div>
        </div>
        <div class="col-lg">
          <div class="mb-3">
            <label class="form-label required">转移最小文件大小(MB) <span class="form-help" title="小于该大小的文件将会忽略，不进行转移重命名"
                data-bs-toggle="tooltip">?</span></label>
            <input type="text" value="{{ Config.media.min_filesize or '' }}" class="form-control"
              id="media.min_filesize" placeholder="200" autocomplete="off">
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-lg">
          <div class="mb-3">
            <label class="form-label">文件路径转移忽略词 <span class="form-help" title="文件路径包含忽略词的，忽略转移，支持正则表达式，注意特殊字符转义"
                data-bs-toggle="tooltip">?</span></label>
            <input type="text" value="{{ Config.media.ignored_paths or '' }}" class="form-control"
              id="media.ignored_paths" placeholder="支持正则表达式，使用;分隔" autocomplete="off">
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-lg">
          <div class="mb-3">
            <label class="form-label">文件名转移忽略词 <span class="form-help" title="文件名（包括扩展名）包含忽略词的，忽略转移，支持正则表达式，注意特殊字符转义"
                data-bs-toggle="tooltip">?</span></label>
            <input type="text" value="{{ Config.media.ignored_files or '' }}" class="form-control"
              id="media.ignored_files" placeholder="支持正则表达式，使用;分隔" autocomplete="off">
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-lg">
          <div class="mb-3">
            <label class="form-label required">电影重命名格式 <span class="form-help"
                title="程序会按定义的命名格式对电影进行重命名；/代表上下级目录，{}内为占位符，转义为&#123;&#123;&#125;&#125;；占位符会使用文件识别出来的实际值替换；占位符外的字符会当成普通字符，直接体现在名称上。<br>电影占位符：<br>{title}：标题<br>{en_title}：英文标题<br>{original_title}：原语种标题<br>{original_name}：原文件名<br>{rev_name}：识别词处理后的原文件名<br>{year}：年份<br>{edition}：版本(Bluray/WEB-DL等)<br>{videoFormat}：分辨率(1080p/4k等)<br>{videoCodec}：视频编码<br>{audioCodec}：音频编码及声道<br>{tmdbid}：TMDB的ID<br>{imdbid}：IMDB的ID<br>{part}：part1/disc1/dvd1<br>{releaseGroup}：制作组/字幕组<br>{name}：识别名称（文件名中识别出的名称，可用自定义识别词替换）<br><br>重命名使用Python的format格式化，可进行指定格式输出"
                data-bs-toggle="tooltip" data-bs-html="true">?</span></label>
            <input type="text" value="{{ Config.media.movie_name_format or '' }}" class="form-control"
              id="media.movie_name_format" placeholder="{title} ({year})/{title}-{part} ({year}) - {videoFormat}"
              autocomplete="off">
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-lg">
          <div class="mb-3">
            <label class="form-label required">剧集重命名格式 <span class="form-help"
                title="程序会按定义的命名格式对电视剧进行重命名；/代表上下级目录，{}内为占位符，转义为&#123;&#123;&#125;&#125;；占位符会使用文件识别出来的实际值替换，占位符外的字符会当成普通字符，直接体现在名称上。<br>电视剧占位符：<br>{title}：标题<br>{en_title}：英文标题<br>{original_title}：原语种标题<br>{original_name}：原文件名<br>{rev_name}：识别词处理后的原文件名<br>{year}：年份<br>{edition}：版本(Bluray/WEB-DL等)<br>{videoFormat}：分辨率(1080p/4k等)<br>{videoCodec}：视频编码<br>{audioCodec}：音频编码及声道<br>{tmdbid}：TMDB的ID<br>{imdbid}：IMDB的ID<br>{season}：季数<br>{episode}：集数<br>{episode_title}：集标题<br>{season_episode}：剧集SxxExx<br>{part}：part1/disc1/dvd1<br>{releaseGroup}：制作组/字幕组<br>{name}：识别名称（文件名中识别出的名称，可用自定义识别词替换）<br><br>重命名使用Python的format格式化，可进行指定格式输出"
                data-bs-toggle="tooltip" data-bs-html="true">?</span></label>
            <input type="text" value="{{ Config.media.tv_name_format or '' }}" class="form-control"
              id="media.tv_name_format"
              placeholder="{title} ({year})/Season {season}/{title}-{part} - {season_episode} - 第 {episode} 集"
              autocomplete="off">
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-12 col-xl-3">
          <div class="mb-3">
            <label class="form-check form-switch">
              <input class="form-check-input" type="checkbox" id="media.filesize_cover" {% if
                Config.media.filesize_cover %}checked{% endif %}>
              <span class="form-check-label">高质量文件覆盖 <span class="form-help"
                  title="开启后，如下载了更高质量的同名文件时，会覆盖媒体库中已有的文件，否则不会进行转移处理" data-bs-toggle="tooltip">?</span></span>
            </label>
          </div>
        </div>
        <div class="col-12 col-xl-3">
          <div class="mb-3">
            <label class="form-check form-switch">
              <input class="form-check-input" type="checkbox" id="media.nfo_poster" {% if Config.media.nfo_poster
                %}checked{% endif %}>
              <span class="form-check-label">刮削元数据及图片 <span class="form-help"
                  title="开启后会自动生成nfo描述文件及图片，协助媒体服务器识别和刮削，在刮削设置中自定义刮削内容" data-bs-toggle="tooltip">?</span></span>
            </label>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="card">
    <div class="card-header">
      <h4 class="card-title"><strong>服务配置</strong></h4>
      <a href="javascript:void(0)" onclick="slideToggle('basic_service')" class="btn-action ms-1" title="展开/折叠">
        {{ SVG.menu_2() }}
      </a>
    </div>
    <div class="card-body" id="basic_service" style="display: none;">
      <div class="row">
        <div class="col-lg">
          <div class="mb-3">
            <label class="form-label required">下载优先规则 <span class="form-help"
                title="订阅及远程搜索下载将按此优先规则选择下载资源，其中站点优先决定于维护的站点优先级或索引器中配置的站点顺序，默认为站点优先"
                data-bs-toggle="tooltip">?</span></label>
            <select class="form-select" id="pt.download_order">
              <option value="" {% if not Config.pt.download_order %}selected{% endif %}>默认</option>
              <option value="site" {% if Config.pt.download_order=='site' %}selected{% endif %}>站点优先</option>
              <option value="seeder" {% if Config.pt.download_order=='seeder' %}selected{% endif %}>做种数优先
              </option>
            </select>
          </div>
        </div>
        <div class="col-lg">
          <div class="mb-3">
            <label class="form-label">订阅RSS周期(秒) <span class="form-help"
                title="RSS订阅刷新的时间间隔，需要在订阅管理中设置订阅站点；如配置为空则不启动RSS订阅功能；为了减小站点压力，最小周期不能小于300秒"
                data-bs-toggle="tooltip">?</span></label>
            <input type="text" value="{{ Config.pt.pt_check_interval or '' }}" class="form-control"
              id="pt.pt_check_interval" placeholder="留空关闭RSS订阅" autocomplete="off">
          </div>
        </div>
        <div class="col-lg">
          <div class="mb-3">
            <label class="form-label">订阅搜索周期(小时) <span class="form-help"
                title="定时对电影/电视剧订阅进行站点存量资源检索下载，用于对RSS订阅进行查漏补缺。以小时为单位，最小间隔为1小时，设置小于1小时时将强制设定为1小时。该项会对站点造成压力，应尽量通过维护站点RSS实现订阅追新，如非必要请不要开启"
                data-bs-toggle="tooltip">?</span></label>
            <input type="text" value="{{ Config.pt.search_rss_interval or '' }}" class="form-control"
              id="pt.search_rss_interval" placeholder="留空关闭订阅定时搜索" autocomplete="off">
          </div>
        </div>
        <div class="col-lg">
          <div class="mb-3">
            <label class="form-label">站点数据刷新时间 <span class="form-help"
                title="站点数据刷新时间，四种配置方法：1、配置间隔，单位小时，比如23.5；2、配置固定时间，如08:00；3、配置时间范围，如08:00-09:00，表示在该时间范围内随机执行一次；4、配置5位cron表达式，如：0 */6 * * *；配置为空则不启用自动站点数据刷新功能。"
                data-bs-toggle="tooltip">?</span></label>
            <input type="text" value="{{ Config.pt.ptrefresh_date_cron or '' }}" class="form-control"
              id="pt.ptrefresh_date_cron" placeholder="留空关闭自动刷新" autocomplete="off">
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-12 col-xl-3">
          <div class="mb-3">
            <label class="form-check form-switch">
              <input class="form-check-input" type="checkbox" id="pt.search_auto" {% if Config.pt.search_auto
                %}checked{% endif %}>
              <span class="form-check-label">远程搜索自动择优下载 <span class="form-help"
                  title="启用后在微信、Telegram等发送名称后会按配置的下载优先规则及过滤规则自动择优下载，否则需要点击链接跳转后手工选择；注意关闭后需要先维护好 '外网访问地址'，否则无法点击跳转。"
                  data-bs-toggle="tooltip">?</span></span>
            </label>
          </div>
        </div>
        <div class="col-12 col-xl-3">
          <div class="mb-3">
            <label class="form-check form-switch">
              <input class="form-check-input" type="checkbox" id="pt.search_no_result_rss" {% if
                Config.pt.search_no_result_rss %}checked{% endif %}>
              <span class="form-check-label">远程下载不完整自动订阅 <span class="form-help"
                  title="启用后在微信、Telegram发送名称搜索下载不完整时将自动添加订阅" data-bs-toggle="tooltip">?</span></span>
            </label>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="card">
    <div class="card-header">
      <h4 class="card-title"><strong>安全配置</strong></h4>
      <a href="javascript:void(0)" onclick="slideToggle('security')" class="btn-action ms-1" title="展开/折叠">
        {{ SVG.menu_2() }}
      </a>
    </div>
    <div class="card-body" id="security" style="display: none;">
      <div class="row">
        <div class="form-group">
          <label class="control-label">API密钥 <span class="form-help"
              title="使用Jellyseerr、Overseerr等调用本程序订阅接口时，需要在Authorization中填入该密钥" data-bs-toggle="tooltip">?</span>
          </label>
          <div class="col-sm-11">
            <input type="text" id="security.api_key" class="form-control"
              value="{% if Config.security %}{{ Config.security.api_key }}{% endif %}">
          </div>
        </div>
        <div class="form-group">
          <label class="control-label">Webhook白名单 <span class="form-help"
              title="仅允许配置的地址范围内地址调用Webhook，多个地址段用,号分隔，默认为0.0.0.0/0,::/0即不做限制" data-bs-toggle="tooltip">?</span>
          </label>
          <div class="col-sm-11">
            <input type="text" value="{{ Config.security.media_server_webhook_allow_ip.ipv4 or '' }}"
              class="form-control" id="security.media_server_webhook_allow_ip.ipv4" placeholder="允许的IPv4 CIDR"
              autocomplete="off">
            <input type="text" value="{{ Config.security.media_server_webhook_allow_ip.ipv6 or '' }}"
              class="form-control" id="security.media_server_webhook_allow_ip.ipv6" placeholder="允许的IPv6 CIDR"
              autocomplete="off">
          </div>
        </div>
        <div class="form-group">
          <label class="control-label">Telegram白名单 <span class="form-help"
              title="仅接收配置的地址范围内发送的Telegram消息，多个地址段用,号分隔，配置为0.0.0.0/0,::/0则不做限制；使用Telegram WebHook且未做代理转发时推荐IPv4地址设置为：*************/20,**********/22，关闭Telegram WebHook时推荐IPv4地址设置为：127.0.0.1"
              data-bs-toggle="tooltip">?</span>
          </label>
          <div class="col-sm-11">
            <input type="text"
              value="{% if Config.security.telegram_webhook_allow_ip %}{{ Config.security.telegram_webhook_allow_ip.ipv4 or '' }}{% endif %}"
              class="form-control" id="security.telegram_webhook_allow_ip.ipv4" placeholder="允许的IPv4 CIDR"
              autocomplete="off">
            <input type="text"
              value="{% if Config.security.telegram_webhook_allow_ip %}{{ Config.security.telegram_webhook_allow_ip.ipv6 or '' }}{% endif %}"
              class="form-control" id="security.telegram_webhook_allow_ip.ipv6" placeholder="允许的IPv6 CIDR"
              autocomplete="off">
          </div>
        </div>
        <div class="form-group">
          <label class="control-label">Synology Chat白名单 <span class="form-help"
              title="仅接收配置的地址范围内发送的Synology Chat消息，多个地址段用,号分隔，配置为0.0.0.0/0,::/0则不做限制，为了安全建议按实际情况设置"
              data-bs-toggle="tooltip">?</span>
          </label>
          <div class="col-sm-11">
            <input type="text"
              value="{% if Config.security.synology_webhook_allow_ip %}{{ Config.security.synology_webhook_allow_ip.ipv4 or '' }}{% endif %}"
              class="form-control" id="security.synology_webhook_allow_ip.ipv4" placeholder="允许的IPv4 CIDR"
              autocomplete="off">
            <input type="text"
              value="{% if Config.security.synology_webhook_allow_ip %}{{ Config.security.synology_webhook_allow_ip.ipv6 or '' }}{% endif %}"
              class="form-control" id="security.synology_webhook_allow_ip.ipv6" placeholder="允许的IPv6 CIDR"
              autocomplete="off">
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-xl">
          <div class="mb-3">
            <label class="form-check form-switch" style="margin-top: 0.75em;">
              <input class="form-check-input" type="checkbox" id="security.check_apikey" {% if
                Config.security.check_apikey %}checked{% endif %}>
              <span class="form-check-label">验证外部请求的API密钥
                <span class="form-help"
                  title="启用后，需要在原来的NAStool API地址后面拼接 ?apikey=xxx ，xxx 为API密钥；例如Emby回调接口由原来的 http://nastool:3000/emby 改为 http://nastool:3000/emby?apikey=xxx。受影响的接口有：plex、jellyfin、emby、telegram、synology、slack、ical"
                  data-bs-toggle="tooltip">?</span>
              </span>
            </label>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="card">
    <div class="card-header">
      <h4 class="card-title"><strong>实验室</strong></h4>
      <a href="javascript:void(0)" onclick="slideToggle('laboratory')" class="btn-action ms-1" title="展开/折叠">
        {{ SVG.menu_2() }}
      </a>
    </div>
    <div class="card-body" id="laboratory" style="display: none;">
      <div class="row">
        <div class="col-lg">
          <div class="mb-3">
            <label class="form-label">OpenAI API Key <span class="form-help"
                title="在platform.openai.com网站申请API Key，用于使用ChatGPT辅助识别文件名（需要打开增强识别开关）及聊天交互等；填写此项代表启用ChatGPT，根据API使用情况OpenAI官方会收取相应的费用"
                data-bs-toggle="tooltip">?</span></label>
            <input type="text" value="{% if Config.openai %}{{ Config.openai.api_key or '' }}{% endif %}"
              class="form-control" id="openai.api_key" placeholder="sk-xxx" autocomplete="off">
          </div>
        </div>
        <div class="col-lg">
          <div class="mb-3">
            <label class="form-label">OpenAI API Url <span class="form-help"
                title="自定义openai请求地址（请注意格式，请求地址需带有请求头且最后没有/号），不填默认使用https://api.openai.com"
                data-bs-toggle="tooltip">?</span></label>
            <input type="text" value="{% if Config.openai %}{{ Config.openai.api_url or '' }}{% endif %}"
              class="form-control" id="openai.api_url" placeholder="https://api.openai.com" autocomplete="off">
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-12 col-xl-3">
          <div class="mb-3">
            <label class="form-check form-switch">
              <input class="form-check-input" type="checkbox" id="laboratory.chatgpt_enable" {% if Config.laboratory and
                Config.laboratory.chatgpt_enable %}checked{% endif %}>
              <span class="form-check-label">ChatGPT增强识别 <span class="form-help"
                  title="开启后，通过TMDB的API无法识别到媒体信息时，使用ChatGPT识别匹配，需要先填写OpenAI API Key" data-bs-toggle="tooltip">?</span>
              </span>
            </label>
          </div>
        </div>
        <div class="col-12 col-xl-3">
          <div class="mb-3">
            <label class="form-check form-switch">
              <input class="form-check-input" type="checkbox" id="laboratory.search_keyword" {% if Config.laboratory and
                Config.laboratory.search_keyword %}checked{% endif %}>
              <span class="form-check-label">辅助识别 <span class="form-help"
                  title="开启后，无法识别到媒体信息时会尝试猜测和纠正关键词并再次匹配，会大大增加识别耗时，一般情况下不建议开启" data-bs-toggle="tooltip">?</span>
              </span>
            </label>
          </div>
        </div>
        <div class="col-12 col-xl-3">
          <div class="mb-3">
            <label class="form-check form-switch">
              <input class="form-check-input" type="checkbox" id="laboratory.search_tmdbweb" {% if Config.laboratory and
                Config.laboratory.search_tmdbweb %}checked{% endif %}>
              <span class="form-check-label">WEB增强识别 <span class="form-help"
                  title="开启后，通过TMDB的API无法识别到媒体信息时，会尝试通过themoviedb.org网站再次搜索匹配，仅个别极端情况下有效，会大大增加识别耗时，一般情况下不建议开启"
                  data-bs-toggle="tooltip">?</span>
              </span>
            </label>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-12 col-xl-3">
          <div class="mb-3">
            <label class="form-check form-switch">
              <input class="form-check-input" type="checkbox" id="laboratory.use_douban_titles" {% if Config.laboratory
                and Config.laboratory.use_douban_titles %}checked{% endif %}>
              <span class="form-check-label">默认搜索豆瓣资源 <span class="form-help"
                  title="开启将使用豆瓣进行电影电视剧的名称搜索，允许中文名不完整时自动联想，但如豆瓣数据与TMDB不一致时可能会无法搜索；关闭后使用TMDB的数据，对中文支持不友好，但不会有无法搜索的问题"
                  data-bs-toggle="tooltip">?</span></span>
            </label>
          </div>
        </div>
        <div class="col-12 col-xl-3">
          <div class="mb-3">
            <label class="form-check form-switch">
              <input class="form-check-input" type="checkbox" id="laboratory.search_adult" {% if Config.laboratory
                and Config.laboratory.search_adult %}checked{% endif %}>
              <span class="form-check-label">TMDB搜索成人资源 <span class="form-help"
                  title="TMDB默认不返回成人内容" data-bs-toggle="tooltip">?</span>
              </span>
            </label>
          </div>
        </div>
        <div class="col-12 col-xl-3">
          <div class="mb-3">
            <label class="form-check form-switch">
              <input class="form-check-input" type="checkbox" id="laboratory.search_en_title" {% if Config.laboratory
                and Config.laboratory.search_en_title %}checked{% endif %}>
              <span class="form-check-label">搜索优先使用英文名 <span class="form-help"
                  title="开启后对于精确搜索场景将会优先使用英文名搜索，否则优先使用中文名搜索" data-bs-toggle="tooltip">?</span>
              </span>
            </label>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-12 col-xl-3">
          <div class="mb-3">
            <label class="form-check form-switch">
              <input class="form-check-input" type="checkbox" id="laboratory.tmdb_cache_expire" {% if Config.laboratory
                and Config.laboratory.tmdb_cache_expire %}checked{% endif %}>
              <span class="form-check-label">TMDB缓存过期策略 <span class="form-help"
                  title="开启TMDB缓存过期策略后，默认7天过期，过期缓存将被删除,  7天内访问过期时间可以被刷新，建议开启" data-bs-toggle="tooltip">?</span>
              </span>
            </label>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="card-footer">
    <div class="row align-items-center">
      <div class="col"></div>
      <div class="col-auto">
        <a id="basic_system_btn" href="javascript:save_basic_config('sys_config')" class="btn btn-primary">
          保存
        </a>
      </div>
    </div>
  </div>
</div>
<div class="modal modal-blur fade" id="modal-scraper" tabindex="-1" role="dialog" aria-hidden="true"
  data-bs-backdrop="static" data-bs-keyboard="false">
  <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
    <div class="card modal-content"
      style="border-top-left-radius:var(--tblr-modal-inner-border-radius) !important; border-top-right-radius:var(--tblr-modal-inner-border-radius) !important">
      <div class="card-header"
        style="border-top-left-radius:var(--tblr-modal-inner-border-radius) !important; border-top-right-radius:var(--tblr-modal-inner-border-radius) !important">
        <ul class="nav nav-fill card-header-tabs nav-tabs rounded-3" data-bs-toggle="tabs" role="tablist">
          <li class="nav-item" role="presentation">
            <a href="#tabs-scraper-nfo" class="nav-link active" style="justify-content: center" data-bs-toggle="tab"
              aria-selected="true" role="tab">
              <span class="me-2">{{ SVG.info_square_rounded() }}</span>
              元数据
            </a>
          </li>
          <li class="nav-item" role="presentation">
            <a href="#tabs-scraper-pic" class="nav-link" style="justify-content: center" data-bs-toggle="tab"
              aria-selected="false" role="tab" tabindex="-1">
              <span class="me-2">{{ SVG.photo() }}</span>
              图片
            </a>
          </li>
        </ul>
      </div>
      <div class="card-body">
        <div class="tab-content">
          <div class="tab-pane fade active show" id="tabs-scraper-nfo" role="tabpanel">
            <div class="card-body">
              <div class="row">
                <div class="col">
                  <div class="mb-3">
                    <label class="form-label">电影 </label>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-12 col-lg-4">
                  <div class="mb-3">
                    <label class="form-check">
                      <input class="form-check-input" type="checkbox" id="scraper_nfo_movie_basic" {% if ScraperNfo and
                        ScraperNfo.movie.basic %}checked{% endif %}>
                      <span class="form-check-label">基础信息 </span>
                    </label>
                  </div>
                </div>
                <div class="col-12 col-lg-4">
                  <div class="mb-3">
                    <label class="form-check">
                      <input class="form-check-input" type="checkbox" id="scraper_nfo_movie_credits" {% if ScraperNfo
                        and ScraperNfo.movie.credits %}checked{% endif %}>
                      <span class="form-check-label">演职人员 </span>
                    </label>
                  </div>
                </div>
                <div class="col-12 col-lg-4">
                  <div class="mb-3">
                    <label class="form-check">
                      <input class="form-check-input" type="checkbox" id="scraper_nfo_movie_credits_chinese" {% if
                        ScraperNfo and ScraperNfo.movie.credits_chinese %}checked{% endif %}>
                      <span class="form-check-label">演职人员中文 </span>
                    </label>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col">
                  <div class="mb-3">
                    <label class="form-label">电视剧 </label>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-12 col-lg-4">
                  <div class="mb-3">
                    <label class="form-check">
                      <input class="form-check-input" type="checkbox" id="scraper_nfo_tv_basic" {% if ScraperNfo and
                        ScraperNfo.tv.basic %}checked{% endif %}>
                      <span class="form-check-label">基础信息 </span>
                    </label>
                  </div>
                </div>
                <div class="col-12 col-lg-4">
                  <div class="mb-3">
                    <label class="form-check">
                      <input class="form-check-input" type="checkbox" id="scraper_nfo_tv_credits" {% if ScraperNfo and
                        ScraperNfo.tv.credits %}checked{% endif %}>
                      <span class="form-check-label">演职人员 </span>
                    </label>
                  </div>
                </div>
                <div class="col-12 col-lg-4">
                  <div class="mb-3">
                    <label class="form-check">
                      <input class="form-check-input" type="checkbox" id="scraper_nfo_tv_credits_chinese" {% if
                        ScraperNfo and ScraperNfo.tv.credits_chinese %}checked{% endif %}>
                      <span class="form-check-label">演职人员中文 </span>
                    </label>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col">
                  <div class="mb-3">
                    <label class="form-label">电视剧-季-集 </label>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-12 col-lg-4">
                  <div class="mb-3">
                    <label class="form-check">
                      <input class="form-check-input" type="checkbox" id="scraper_nfo_tv_season_basic" {% if ScraperNfo
                        and ScraperNfo.tv.season_basic %}checked{% endif %}>
                      <span class="form-check-label">季-基础信息 </span>
                    </label>
                  </div>
                </div>
                <div class="col-12 col-lg-4">
                  <div class="mb-3">
                    <label class="form-check">
                      <input class="form-check-input" type="checkbox" id="scraper_nfo_tv_episode_basic" {% if ScraperNfo
                        and ScraperNfo.tv.episode_basic %}checked{% endif %}>
                      <span class="form-check-label">集-基础信息 </span>
                    </label>
                  </div>
                </div>
                <div class="col-12 col-lg-4">
                  <div class="mb-3">
                    <label class="form-check">
                      <input class="form-check-input" type="checkbox" id="scraper_nfo_tv_episode_credits" {% if
                        ScraperNfo and ScraperNfo.tv.episode_credits %}checked{% endif %}>
                      <span class="form-check-label">集-演职人员 </span>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="tab-pane fade" id="tabs-scraper-pic" role="tabpanel">
            <div class="card-body">
              <div class="row">
                <div class="col">
                  <div class="mb-3">
                    <label class="form-label">电影图片 </label>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-12 col-lg-3">
                  <div class="mb-3">
                    <label class="form-check">
                      <input class="form-check-input" type="checkbox" id="scraper_pic_movie_poster" {% if ScraperPic and
                        ScraperPic.movie.poster %}checked{% endif %}>
                      <span class="form-check-label">poster </span>
                    </label>
                  </div>
                </div>
                <div class="col-12 col-lg-3">
                  <div class="mb-3">
                    <label class="form-check">
                      <input class="form-check-input" type="checkbox" id="scraper_pic_movie_backdrop" {% if ScraperPic
                        and ScraperPic.movie.backdrop %}checked{% endif %}>
                      <span class="form-check-label">fanart </span>
                    </label>
                  </div>
                </div>
                <div class="col-12 col-lg-3">
                  <div class="mb-3">
                    <label class="form-check">
                      <input class="form-check-input" type="checkbox" id="scraper_pic_movie_background" {% if ScraperPic
                        and ScraperPic.movie.background %}checked{% endif %}>
                      <span class="form-check-label">background </span>
                    </label>
                  </div>
                </div>
                <div class="col-12 col-lg-3">
                  <div class="mb-3">
                    <label class="form-check">
                      <input class="form-check-input" type="checkbox" id="scraper_pic_movie_logo" {% if ScraperPic and
                        ScraperPic.movie.logo %}checked{% endif %}>
                      <span class="form-check-label">logo </span>
                    </label>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-12 col-lg-3">
                  <div class="mb-3">
                    <label class="form-check">
                      <input class="form-check-input" type="checkbox" id="scraper_pic_movie_disc" {% if ScraperPic and
                        ScraperPic.movie.disc %}checked{% endif %}>
                      <span class="form-check-label">disc </span>
                    </label>
                  </div>
                </div>
                <div class="col-12 col-lg-3">
                  <div class="mb-3">
                    <label class="form-check">
                      <input class="form-check-input" type="checkbox" id="scraper_pic_movie_banner" {% if ScraperPic and
                        ScraperPic.movie.banner %}checked{% endif %}>
                      <span class="form-check-label">banner </span>
                    </label>
                  </div>
                </div>
                <div class="col-12 col-lg-3">
                  <div class="mb-3">
                    <label class="form-check">
                      <input class="form-check-input" type="checkbox" id="scraper_pic_movie_thumb" {% if ScraperPic and
                        ScraperPic.movie.thumb %}checked{% endif %}>
                      <span class="form-check-label">thumb </span>
                    </label>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col">
                  <div class="mb-3">
                    <label class="form-label">电视剧图片 </label>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-12 col-lg-3">
                  <div class="mb-3">
                    <label class="form-check">
                      <input class="form-check-input" type="checkbox" id="scraper_pic_tv_poster" {% if ScraperPic and
                        ScraperPic.tv.poster %}checked{% endif %}>
                      <span class="form-check-label">poster </span>
                    </label>
                  </div>
                </div>
                <div class="col-12 col-lg-3">
                  <div class="mb-3">
                    <label class="form-check">
                      <input class="form-check-input" type="checkbox" id="scraper_pic_tv_backdrop" {% if ScraperPic and
                        ScraperPic.tv.backdrop %}checked{% endif %}>
                      <span class="form-check-label">fanart </span>
                    </label>
                  </div>
                </div>
                <div class="col-12 col-lg-3">
                  <div class="mb-3">
                    <label class="form-check">
                      <input class="form-check-input" type="checkbox" id="scraper_pic_tv_background" {% if ScraperPic
                        and ScraperPic.tv.background %}checked{% endif %}>
                      <span class="form-check-label">background </span>
                    </label>
                  </div>
                </div>
                <div class="col-12 col-lg-3">
                  <div class="mb-3">
                    <label class="form-check">
                      <input class="form-check-input" type="checkbox" id="scraper_pic_tv_logo" {% if ScraperPic and
                        ScraperPic.tv.logo %}checked{% endif %}>
                      <span class="form-check-label">logo </span>
                    </label>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-12 col-lg-3">
                  <div class="mb-3">
                    <label class="form-check">
                      <input class="form-check-input" type="checkbox" id="scraper_pic_tv_clearart" {% if ScraperPic and
                        ScraperPic.tv.clearart %}checked{% endif %}>
                      <span class="form-check-label">clearart </span>
                    </label>
                  </div>
                </div>
                <div class="col-12 col-lg-3">
                  <div class="mb-3">
                    <label class="form-check">
                      <input class="form-check-input" type="checkbox" id="scraper_pic_tv_banner" {% if ScraperPic and
                        ScraperPic.tv.banner %}checked{% endif %}>
                      <span class="form-check-label">banner </span>
                    </label>
                  </div>
                </div>
                <div class="col-12 col-lg-3">
                  <div class="mb-3">
                    <label class="form-check">
                      <input class="form-check-input" type="checkbox" id="scraper_pic_tv_thumb" {% if ScraperPic and
                        ScraperPic.tv.thumb %}checked{% endif %}>
                      <span class="form-check-label">thumb </span>
                    </label>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col">
                  <div class="mb-3">
                    <label class="form-label">电视剧-季图片 </label>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-12 col-lg-3">
                  <div class="mb-3">
                    <label class="form-check">
                      <input class="form-check-input" type="checkbox" id="scraper_pic_tv_season_poster" {% if ScraperPic
                        and ScraperPic.tv.season_poster %}checked{% endif %}>
                      <span class="form-check-label">poster </span>
                    </label>
                  </div>
                </div>
                <div class="col-12 col-lg-3">
                  <div class="mb-3">
                    <label class="form-check">
                      <input class="form-check-input" type="checkbox" id="scraper_pic_tv_season_banner" {% if ScraperPic
                        and ScraperPic.tv.season_banner %}checked{% endif %}>
                      <span class="form-check-label">banner </span>
                    </label>
                  </div>
                </div>
                <div class="col-12 col-lg-3">
                  <div class="mb-3">
                    <label class="form-check">
                      <input class="form-check-input" type="checkbox" id="scraper_pic_tv_season_thumb" {% if ScraperPic
                        and ScraperPic.tv.season_thumb %}checked{% endif %}>
                      <span class="form-check-label">thumb </span>
                    </label>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col">
                  <div class="mb-3">
                    <label class="form-label">电视剧-集图片 </label>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-12 col-lg-3">
                  <div class="mb-3">
                    <label class="form-check">
                      <input class="form-check-input" type="checkbox" id="scraper_pic_tv_episode_thumb"
                        onclick="open_ffmpeg_div('modal_episode_thumb_ffmpeg')" {% if ScraperPic and
                        ScraperPic.tv.episode_thumb %}checked{% endif %}>
                      <span class="form-check-label">thumb <span class="form-help" title="开启后将查询TMDB生成集缩略图"
                          data-bs-toggle="tooltip">?</span></span>
                    </label>
                  </div>
                </div>
                <div class="col-12 col-lg-3 {% if not ScraperPic or not ScraperPic.tv.episode_thumb %}d-none{% endif %}"
                  id="modal_episode_thumb_ffmpeg">
                  <div class="mb-3">
                    <label class="form-check">
                      <input class="form-check-input" type="checkbox" id="scraper_pic_tv_episode_thumb_ffmpeg" {% if
                        ScraperPic and ScraperPic.tv.episode_thumb and ScraperPic.tv.episode_thumb_ffmpeg %}checked{%
                        endif %}>
                      <span class="form-check-label">thumb-ffmpeg <span class="form-help"
                          title="开始后，如TMDB无数据则读取视频文件生成（需要ffmpeg，最新完整版Docker镜像已包含，lite版本镜像默认不支持该功能）"
                          data-bs-toggle="tooltip">?</span></span>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-link" data-bs-dismiss="modal">取消</button>
        <a id="scraper_save_btn" href="javascript:save_scraper_config()" class="btn btn-primary ms-auto">
          保存
        </a>
      </div>
    </div>
  </div>
</div>
<div class="modal modal-blur fade" id="modal-user-script" tabindex="-1" role="dialog" aria-hidden="true"
  data-bs-backdrop="static" data-bs-keyboard="false">
  <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
    <div class="card modal-content"
      style="border-top-left-radius:var(--tblr-modal-inner-border-radius) !important; border-top-right-radius:var(--tblr-modal-inner-border-radius) !important">
      <div class="card-header"
        style="border-top-left-radius:var(--tblr-modal-inner-border-radius) !important; border-top-right-radius:var(--tblr-modal-inner-border-radius) !important">
        <ul class="nav nav-fill card-header-tabs nav-tabs rounded-3" data-bs-toggle="tabs" role="tablist">
          <li class="nav-item" role="presentation">
            <a href="#tabs-user-css" class="nav-link active" style="justify-content: center" data-bs-toggle="tab"
              aria-selected="true" role="tab">
              CSS
            </a>
          </li>
          <li class="nav-item" role="presentation">
            <a href="#tabs-user-script" class="nav-link" style="justify-content: center" data-bs-toggle="tab"
              aria-selected="false" role="tab" tabindex="-1">
              JavaScript
            </a>
          </li>
        </ul>
      </div>
      <div class="card-body">
        <div class="tab-content">
          <div class="tab-pane fade active show" id="tabs-user-css" role="tabpanel">
            <div class="form-control" id="user_script_css" style="height: 20rem;">{{ CustomScriptCfg.css or '' }}</div>
          </div>
          <div class="tab-pane fade" id="tabs-user-script" role="tabpanel">
            <div class="form-control" id="user_script_javascript" style="height: 20rem;">{{ CustomScriptCfg.javascript
              or '' }}</div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-link" data-bs-dismiss="modal">取消</button>
        <a id="user_script_save_btn" href="javascript:save_user_script()" class="btn btn-primary ms-auto">
          保存
        </a>
      </div>
    </div>
  </div>
</div>
<div class="modal modal-blur fade" id="modal-library-category" tabindex="-1" role="dialog" aria-hidden="true"
  data-bs-backdrop="static" data-bs-keyboard="false">
  <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">二级分类配置</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="form-control" id="library_category_config" style="height: calc(100vh - 20rem);"></div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-link me-auto" data-bs-dismiss="modal">取消</button>
        <a href="javascript:save_category_config()" id="library_category_save_btn" class="btn btn-primary">保存</a>
      </div>
    </div>
  </div>
</div>

<div class="modal modal-blur fade" id="modal-dirctory" tabindex="-1" role="dialog" aria-hidden="true"
  data-bs-backdrop="static" data-bs-keyboard="false">
  <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">新增目录</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-lg-12">
            <div class="mb-3">
              <label class="form-label">路径 <span class="form-help"
                  title="Emby/Jellyfin/Plex媒体库对应文件的路径，下载文件转移、目录同步未配置目的目录时，媒体文件将重命名转移到该目录"
                  data-bs-toggle="tooltip">?</span></label>
              <input type="text" value="" id="path_str" class="form-control filetree-folders-only" autocomplete="off">
              <input type="hidden" value="" id="path_type" class="form-control">
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-link me-auto" data-bs-dismiss="modal">取消</button>
        <a href="javascript:add_directory_config()" id="directory_save_btn" class="btn btn-primary">确定</a>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
  // CSS编辑器
  css_editor = ace.edit("user_script_css");
  localStorage.getItem("tablerTheme") === "dark" ? css_editor.setTheme("ace/theme/one_dark") : css_editor.setTheme("ace/theme/xcode");
  css_editor.session.setMode("ace/mode/css");
  css_editor.setOptions({
    fontFamily: "Consolas, Monaco, monospace",
    fontSize: "10pt"
  });
  // JavaScript编辑器
  javascript_editor = ace.edit("user_script_javascript");
  localStorage.getItem("tablerTheme") === "dark" ? javascript_editor.setTheme("ace/theme/one_dark") : javascript_editor.setTheme("ace/theme/xcode");
  javascript_editor.session.setMode("ace/mode/javascript");
  javascript_editor.setOptions({
    fontFamily: "Consolas, Monaco, monospace",
    fontSize: "10pt"
  });

  // 重新渲染部分元素
  function render_part(element_id) {

    if (!element_id) {
      return;
    }

    var page = window.history.state?.page.replaceAll(" ", "%20");
    if (!page) {
      return;
    }

    if (!element_id.startsWith("#")) {
      element_id = '#' + element_id;
    }

    $.ajax({
      url: page,
      dataType: 'html',
      success: function (data) {
        // 创建一个临时的 DOM 元素来解析响应 HTML
        var $response = $('<div>').html(data);
        // 修复登录页面刷新问题
        if ($response.find("title").first().text() === "登录 - NAStool") {
          // 刷新页面
          window.location.reload();
          return;
        }
        // 从响应中提取指定 ID 的元素
        var $newContent = $response.find(element_id);
        // 用新内容替换当前页面中的对应元素
        $(element_id).replaceWith($newContent);

        // 展示该面板
        if ($(element_id).hasClass('tab-pane')) {
          $(element_id).addClass('active show');
        } else {
          $(element_id).show();
        }

      }
    });

  }

  // 保存基础设置
  function save_basic_config(type, flag = true) {
    let params = input_select_GetVal(type);
    if (params["media.category"] === "config") {
      show_warning_modal("非法二级分类策略名称");
      return;
    }
    if (flag) {
      $("#" + type + "_btn").text("保存中...").attr("disabled", true);
    }
    axios_post("update_config", params, function (ret) {
      if (flag) {
        $("#" + type + "_btn").text("保存").attr("disabled", false);
        // window_history_refresh();
        show_success_modal("配置已保存!");
      }
    });
  }

  // 展开/收起div
  function slideToggle(id) {
    $(`#${id}`).slideToggle();
  }

  // 保存刮削配置
  function save_scraper_config() {
    debugger;
    let params = {
      "key": "UserScraperConf",
      "value": {
        scraper_nfo: {
          movie: {
            basic: $("#scraper_nfo_movie_basic").prop('checked'),
            credits: $("#scraper_nfo_movie_credits").prop('checked'),
            credits_chinese: $("#scraper_nfo_movie_credits_chinese").prop('checked'),
          },
          tv: {
            basic: $("#scraper_nfo_tv_basic").prop('checked'),
            credits: $("#scraper_nfo_tv_credits").prop('checked'),
            credits_chinese: $("#scraper_nfo_tv_credits_chinese").prop('checked'),
            season_basic: $("#scraper_nfo_tv_season_basic").prop('checked'),
            episode_basic: $("#scraper_nfo_tv_episode_basic").prop('checked'),
            episode_credits: $("#scraper_nfo_tv_episode_credits").prop('checked'),
          }
        },
        scraper_pic: {
          movie: {
            poster: $("#scraper_pic_movie_poster").prop('checked'),
            backdrop: $("#scraper_pic_movie_backdrop").prop('checked'),
            background: $("#scraper_pic_movie_background").prop('checked'),
            logo: $("#scraper_pic_movie_logo").prop('checked'),
            disc: $("#scraper_pic_movie_disc").prop('checked'),
            banner: $("#scraper_pic_movie_banner").prop('checked'),
            thumb: $("#scraper_pic_movie_thumb").prop('checked'),
          },
          tv: {
            poster: $("#scraper_pic_tv_poster").prop('checked'),
            backdrop: $("#scraper_pic_tv_backdrop").prop('checked'),
            background: $("#scraper_pic_tv_background").prop('checked'),
            logo: $("#scraper_pic_tv_logo").prop('checked'),
            clearart: $("#scraper_pic_tv_clearart").prop('checked'),
            banner: $("#scraper_pic_tv_banner").prop('checked'),
            thumb: $("#scraper_pic_tv_thumb").prop('checked'),
            season_poster: $("#scraper_pic_tv_season_poster").prop('checked'),
            season_banner: $("#scraper_pic_tv_season_banner").prop('checked'),
            season_thumb: $("#scraper_pic_tv_season_thumb").prop('checked'),
            episode_thumb: $("#scraper_pic_tv_episode_thumb").prop('checked'),
            episode_thumb_ffmpeg: $("#scraper_pic_tv_episode_thumb_ffmpeg").prop('checked')
          }
        }
      }
    };
    axios_post("set_system_config", params, function (ret) {
      $("#modal-scraper").modal("hide");
      window_history_refresh();
    });
  }

  // 保存自定义CSS/JavsScript配置
  function save_user_script() {
    let css_text = css_editor.getValue();
    let javascript_text = javascript_editor.getValue();
    $("#user_script_save_btn").text("保存中...").attr("disabled", true);
    axios_post("save_user_script", { css: css_text, javascript: javascript_text }, function (ret) {
      $("#user_script_save_btn").text("保存").attr("disabled", false);
    });
    $("#modal-user-script").modal("hide");
  }

  // 选中电视剧-集图片thumb，显示ffmpeg
  function open_ffmpeg_div(div_id) {
    let ffmpeg_container = $("#" + div_id);
    let ffmpeg_check = ffmpeg_container.find("input[type='checkbox']");
    if (ffmpeg_container.hasClass("d-none")) {
      ffmpeg_container.removeClass("d-none");
    } else {
      ffmpeg_container.addClass("d-none");
      ffmpeg_check.prop("checked", false);
    }
  }

  // 显示二级分类编辑框
  function show_category_config_modal() {
    let category_name = $("[name=category_name]").val();
    if (!category_name) {
      show_warning_modal("请先输入二级分类策略名称");
      return;
    }
    if (category_name === "config") {
      show_warning_modal("非法二级分类策略名称");
      return;
    }
    axios_post("get_category_config", { "category_name": category_name }, function (ret) {
      if (ret.code === 0) {
        category_editor.setValue(ret.text);
        $("#modal-library-category").modal('show');
      } else {
        show_warning_modal(ret.msg);
      }
    });
  }

  // 保存二级分类配置
  function save_category_config() {
    const value = category_editor.getValue();
    const params = { "config": value };
    axios_post("update_category_config", params, function (ret) {
      $("#modal-library-category").modal('hide');
    });
  }

  // JavaScript编辑器
  category_editor = ace.edit("library_category_config");
  localStorage.getItem("tablerTheme") === "dark" ? category_editor.setTheme("ace/theme/one_dark") : category_editor.setTheme("ace/theme/xcode");
  category_editor.session.setMode("ace/mode/yaml");
  category_editor.setOptions({
    fontFamily: "Consolas, Monaco, monospace",
    fontSize: "10pt"
  });

</script>